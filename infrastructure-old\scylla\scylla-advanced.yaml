---
apiVersion: v1
kind: ConfigMap
metadata:
  name: scylla-advanced-dashboard
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: ScyllaDB
data:
  scylla-advanced-dashboard.json: |-
    {"annotations":{"list":[{"builtIn":1,"datasource":"-- Grafana --","enable":true,"hide":true,"iconColor":"rgba(0, 211, 255, 1)","name":"Annotations & Alerts","type":"dashboard"},{"class":"annotation_restart","datasource":"Prometheus","enable":true,"expr":"resets(scylla_gossip_heart_beat{cluster=\"$cluster\"}[$__rate_interval])>0","hide":false,"iconColor":"rgba(255, 96, 96, 1)","limit":100,"name":"node_restart","showIn":0,"tagKeys":"instance,dc,cluster","tags":[],"titleFormat":"restart","type":"tags"},{"class":"annotation_stall","datasource":"Prometheus","enable":false,"expr":"changes(scylla_stall_detector_reported{cluster=\"$cluster\"}[$__rate_interval])>0","hide":false,"iconColor":"rgba(255, 96, 96, 1)","limit":100,"name":"stall detector","showIn":0,"tagKeys":"dc,instance,shard","tags":[],"titleFormat":"Stall found","type":"tags"},{"class":"annotation_schema_changed","datasource":"Prometheus","enable":false,"expr":"changes(scylla_database_schema_changed{cluster=\"$cluster\"}[$__rate_interval])>0","hide":false,"iconColor":"rgba(255, 96, 96, 1)","limit":100,"name":"Schema Changed","showIn":0,"tagKeys":"instance,dc,cluster","tags":[],"titleFormat":"schema changed","type":"tags"}]},"editable":true,"gnetId":null,"graphTooltip":1,"iteration":1728458975884,"links":[{"asDropdown":true,"icon":"external link","includeVars":true,"keepTime":true,"tags":[],"type":"dashboards"}],"panels":[{"class":"text_panel","dashproductreject":"no-version-check","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":1,"w":9,"x":0,"y":0},"id":1,"isNew":true,"links":[],"mode":"html","options":{"content":"<img src=\"https://repositories.scylladb.com/scylla/imgversion/$monitoring_version/scylla-monitoring\">","mode":"html"},"pluginVersion":"7.4.2","span":12,"title":"","type":"text"},{"class":"text_panel","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":1,"w":4,"x":9,"y":0},"id":2,"isNew":true,"links":[],"mode":"html","options":{"content":"# ","mode":"markdown"},"pluginVersion":"7.4.2","span":12,"title":"","type":"text"},{"class":"text_panel","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":1,"w":4,"x":13,"y":0},"id":3,"isNew":true,"links":[],"mode":"html","options":{"content":"# ","mode":"markdown"},"pluginVersion":"7.4.2","span":12,"title":"","type":"text"},{"class":"text_panel","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":1,"w":7,"x":17,"y":0},"id":4,"isNew":true,"links":[],"mode":"html","options":{"content":"# ","mode":"markdown"},"pluginVersion":"7.4.2","span":12,"title":"","type":"text"},{"class":"small_stat","datasource":"Prometheus","description":"The number of nodes configured in the cluster.","fieldConfig":{"defaults":{"custom":{},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null}]}},"overrides":[]},"gridPos":{"h":4,"w":2,"x":0,"y":1},"id":5,"options":{"colorMode":"value","graphMode":"none","justifyMode":"auto","orientation":"auto","reduceOptions":{"calcs":["last"],"fields":"","values":false},"text":{},"textMode":"auto"},"pluginVersion":"7.4.2","targets":[{"expr":"count(scylla_scylladb_current_version{job=\"scylla-db-scylla-exporter-sc\", cluster=\"$cluster\"})","interval":"","intervalFactor":1,"legendFormat":"Total Nodes","refId":"A","step":40}],"timeFrom":null,"timeShift":null,"title":"# Nodes","type":"stat"},{"class":"small_stat","datasource":"Prometheus","description":"The number of unreachable nodes.\nUsually because a machine is down or unreachable.","fieldConfig":{"defaults":{"custom":{},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"green","value":0},{"color":"red","value":1}]}},"overrides":[]},"gridPos":{"h":4,"w":2,"x":2,"y":1},"id":6,"options":{"colorMode":"value","graphMode":"none","justifyMode":"auto","orientation":"auto","reduceOptions":{"calcs":["last"],"fields":"","values":false},"text":{},"textMode":"auto"},"pluginVersion":"7.4.2","targets":[{"expr":"(count(scrape_samples_scraped{job=\"scylla\", cluster=\"$cluster\"}==0) OR vector(0))","intervalFactor":1,"legendFormat":"Offline ","refId":"A","step":20}],"timeFrom":null,"timeShift":null,"title":"Unreachable","type":"stat"},{"class":"small_stat","datasource":"Prometheus","description":"The number of joining and leaving nodes.\nThe number of nodes that are up but not actively part of the cluster, either because they are still joining or because they are leaving.","fieldConfig":{"defaults":{"custom":{},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"green","value":0},{"color":"red","value":1}]}},"overrides":[]},"gridPos":{"h":4,"w":2,"x":4,"y":1},"id":7,"options":{"colorMode":"value","graphMode":"none","justifyMode":"auto","orientation":"auto","reduceOptions":{"calcs":["last"],"fields":"","values":false},"text":{},"textMode":"auto"},"pluginVersion":"7.4.2","targets":[{"expr":"count(scylla_node_operation_mode{cluster=\"$cluster\"}!=3)OR vector(0)","intervalFactor":1,"legendFormat":"Offline ","refId":"A","step":20}],"thresholds":"1,2","timeFrom":null,"timeShift":null,"title":"Inactive","type":"stat"},{"class":"small_stat","datasource":"Prometheus","fieldConfig":{"defaults":{"custom":{},"mappings":[],"noValue":" Offline","thresholds":{"mode":"absolute","steps":[{"color":"red","value":null},{"color":"green","value":0}]}},"overrides":[{"matcher":{"id":"byFrameRefID","options":"A"},"properties":[{"id":"mappings","value":[{"from":"","id":1,"text":"Offline","to":"","type":1,"value":"-1"},{"from":"","id":2,"text":"Backup Repair","to":"","type":1,"value":"3"},{"from":"","id":3,"text":"Online","to":"","type":1,"value":"0"},{"from":"","id":4,"text":"Repair","to":"","type":1,"value":"1"},{"from":"","id":5,"text":"Backup","to":"","type":1,"value":"2"}]}]},{"matcher":{"id":"byFrameRefID","options":"B"},"properties":[{"id":"unit","value":"percent"},{"id":"mappings","value":[{"from":"","id":1,"text":" ","to":"","type":1,"value":"0"}]}]}]},"gridPos":{"h":4,"w":3,"x":6,"y":1},"id":8,"options":{"colorMode":"value","graphMode":"none","justifyMode":"auto","orientation":"auto","percentChangeColorMode":"standard","reduceOptions":{"calcs":["last"],"fields":"","values":false},"showPercentChange":false,"text":{},"textMode":"value","wideLayout":true},"pluginVersion":"7.4.2","targets":[{"expr":"(max(scylla_manager_scheduler_run_indicator{type=~\"repair\",cluster=\"$cluster\"}) or on() vector(0)) + (max(scylla_manager_scheduler_run_indicator{type=~\"backup\",cluster=\"$cluster\"})*2 or on() vector(0)) + (sum(scylla_manager_server_current_version{}) or on() vector(-1))","intervalFactor":1,"refId":"A","step":40},{"expr":"(avg(scylla_manager_repair_progress{cluster=\"$cluster\", job=\"scylla_manager\"}) * max(scylla_manager_scheduler_run_indicator{type=\"repair\",cluster=\"$cluster\"})) or on ()  (100*avg(manager:backup_progress{cluster=\"$cluster\"}) * max(scylla_manager_scheduler_run_indicator{type=\"backup\",cluster=\"$cluster\"})) or on () vector(0)","instant":true,"interval":"","legendFormat":"","refId":"B"}],"timeFrom":null,"timeShift":null,"title":"Manager","type":"stat"},{"class":"small_stat","datasource":"Prometheus","description":"Average Write Latency","fieldConfig":{"defaults":{"custom":{},"decimals":0,"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":50000}]},"unit":"µs"},"overrides":[]},"gridPos":{"h":4,"w":2,"x":9,"y":1},"id":9,"options":{"colorMode":"value","graphMode":"none","justifyMode":"auto","orientation":"auto","reduceOptions":{"calcs":["last"],"fields":"","values":false},"text":{},"textMode":"auto"},"pluginVersion":"7.4.2","targets":[{"expr":"avg(wlatencya{by=\"cluster\", cluster=~\"$cluster|^$\",scheduling_group_name=~\"$sg\"}>0)","instant":true,"intervalFactor":1,"legendFormat":"","refId":"A","step":4}],"timeFrom":null,"timeShift":null,"title":"Avg Write","type":"stat"},{"class":"small_stat","datasource":"Prometheus","description":"99% write Latency","fieldConfig":{"defaults":{"custom":{},"decimals":0,"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":100000}]},"unit":"µs"},"overrides":[]},"gridPos":{"h":4,"w":2,"x":11,"y":1},"id":10,"options":{"colorMode":"value","graphMode":"none","justifyMode":"auto","orientation":"auto","reduceOptions":{"calcs":["last"],"fields":"","values":false},"text":{},"textMode":"auto"},"pluginVersion":"7.4.2","targets":[{"expr":"wlatencyp99{by=\"cluster\", cluster=~\"$cluster|^$\",scheduling_group_name=~\"$sg\"}>0","instant":true,"intervalFactor":1,"legendFormat":"{{scheduling_group_name}}","refId":"A","step":4}],"timeFrom":null,"timeShift":null,"title":"99% Write","type":"stat"},{"class":"small_stat","datasource":"Prometheus","description":"Average Read Latency","fieldConfig":{"defaults":{"custom":{},"decimals":0,"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":50000}]},"unit":"µs"},"overrides":[]},"gridPos":{"h":4,"w":2,"x":13,"y":1},"id":11,"options":{"colorMode":"value","graphMode":"none","justifyMode":"auto","orientation":"auto","reduceOptions":{"calcs":["last"],"fields":"","values":false},"text":{},"textMode":"auto"},"pluginVersion":"7.4.2","targets":[{"expr":"avg(rlatencya{by=\"cluster\", cluster=~\"$cluster|^$\",scheduling_group_name=~\"$sg\"}>0)","instant":true,"intervalFactor":1,"legendFormat":"","refId":"A","step":4}],"timeFrom":null,"timeShift":null,"title":"Avg Read","type":"stat"},{"class":"small_stat","datasource":"Prometheus","description":"99% read Latency","fieldConfig":{"defaults":{"custom":{},"decimals":0,"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":100000}]},"unit":"µs"},"overrides":[]},"gridPos":{"h":4,"w":2,"x":15,"y":1},"id":12,"options":{"colorMode":"value","graphMode":"none","justifyMode":"auto","orientation":"auto","reduceOptions":{"calcs":["last"],"fields":"","values":false},"text":{},"textMode":"auto"},"pluginVersion":"7.4.2","targets":[{"expr":"rlatencyp99{by=\"cluster\", cluster=~\"$cluster|^$\",scheduling_group_name=~\"$sg\"}>0","instant":true,"intervalFactor":1,"legendFormat":"{{scheduling_group_name}}","refId":"A","step":4}],"timeFrom":null,"timeShift":null,"title":"99% Read","type":"stat"},{"class":"small_stat","datasource":"Prometheus","fieldConfig":{"defaults":{"custom":{},"decimals":1,"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null}]},"unit":"si:"},"overrides":[]},"gridPos":{"h":4,"w":3,"x":17,"y":1},"id":13,"options":{"colorMode":"value","graphMode":"none","justifyMode":"auto","orientation":"auto","reduceOptions":{"calcs":["last"],"fields":"","values":false},"text":{},"textMode":"auto"},"pluginVersion":"7.4.2","targets":[{"expr":"sum(rate(scylla_transport_requests_served{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) + (sum(rate(scylla_thrift_served{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) or on() vector(0))","instant":true,"intervalFactor":1,"refId":"A","step":40}],"timeFrom":null,"timeShift":null,"title":"Requests/s","type":"stat"},{"class":"small_stat","datasource":"Prometheus","description":"The percentage of the time during which Scylla utilized the CPU. Note that because Scylla does busy polling for some time before going idle, CPU utilization as seen by the operating system may be much higher. Your system is not yet CPU-bottlenecked until this metric is high.","fieldConfig":{"defaults":{"custom":{},"decimals":0,"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"percent"},"overrides":[]},"gridPos":{"h":4,"w":2,"x":20,"y":1},"id":14,"options":{"colorMode":"value","graphMode":"none","justifyMode":"auto","orientation":"auto","reduceOptions":{"calcs":["last"],"fields":"","values":false},"text":{},"textMode":"auto"},"pluginVersion":"7.4.2","targets":[{"expr":"avg(scylla_reactor_utilization{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} )","instant":true,"intervalFactor":1,"legendFormat":"","refId":"A","step":4}],"timeFrom":null,"timeShift":null,"title":"Load","type":"stat"},{"class":"small_stat","datasource":"Prometheus","description":"The rate of timeouts (read and write).\n\nTimeouts are an indication of an overloaded system","fieldConfig":{"defaults":{"custom":{},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":1}]}},"overrides":[]},"gridPos":{"h":4,"w":2,"x":22,"y":1},"id":15,"options":{"colorMode":"value","graphMode":"none","justifyMode":"auto","orientation":"auto","reduceOptions":{"calcs":["last"],"fields":"","values":false},"text":{},"textMode":"auto"},"pluginVersion":"7.4.2","targets":[{"expr":"(sum(rate(scylla_storage_proxy_coordinator_write_timeouts{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) or on() vector(0)) + (sum(rate(scylla_storage_proxy_coordinator_read_timeouts{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) or on() vector(0))","instant":true,"intervalFactor":1,"refId":"A","step":40}],"timeFrom":null,"timeShift":null,"title":"Timeouts","type":"stat"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":5},"id":16,"panels":[],"repeat":"","title":"","type":"row"},{"class":"plain_text","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":2,"w":24,"x":0,"y":6},"id":17,"isNew":true,"links":[],"options":{"content":"<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">IO Queue Information</h1>","mode":"html"},"pluginVersion":"7.4.2","span":12,"style":{},"title":"","transparent":true,"type":"text"},{"class":"percentunit_panel","datasource":"Prometheus","description":"This graph shows how IO queue consumption is distributed between io-groups and streams.\n\nscylla_io_queue_consumption","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMax":1,"axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"percentunit"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":8},"id":18,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"$func(rate(scylla_io_queue_consumption{iogroup=~\"$iogroup\", class=~\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\"}[$__rate_interval])) by (instance, iogroup, stream)","intervalFactor":1,"legendFormat":"Group {{iogroup}} {{stream}} {{dc}} {{instance}} ","refId":"A","step":30}],"title":"I/O Group consumption by instance","type":"timeseries"},{"class":"percentunit_panel","datasource":"Prometheus","description":"This graph shows the ratio of dispatch rate to completion rate. It is expected to be 1.0, growing larger on reactor stalls or disk problems.\n\nscylla_io_queue_flow_ratio","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMax":1,"axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"percentunit"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":8},"id":19,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], scylla_io_queue_flow_ratio{iogroup=~\"$iogroup\", instance=~\"[[node]]\", cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) or on ([[by]]) bottomk([[bottomk]], scylla_io_queue_flow_ratio{iogroup=~\"$iogroup\", instance=~\"[[node]]\", cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"})","intervalFactor":1,"legendFormat":"Group {{iogroup}} {{dc}} {{instance}} {{shard}}","refId":"A","step":30}],"title":"I/O Group Queue flow ratio","type":"timeseries"},{"collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":14},"id":20,"panels":[],"repeat":"classes","scopedVars":{"classes":{"selected":false,"text":"background_reclaim","value":"background_reclaim"}},"title":"$classes","type":"row"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":15},"id":21,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"classes":{"selected":false,"text":"background_reclaim","value":"background_reclaim"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":15},"id":22,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"classes":{"selected":false,"text":"background_reclaim","value":"background_reclaim"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes Queue length by [[by]]","type":"timeseries"},{"class":"bps_panel","datasource":"Prometheus","description":"The queue bandwidth rate in bytes \n\nscylla_io_queue_total_bytes","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":15},"id":23,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"classes":{"selected":false,"text":"background_reclaim","value":"background_reclaim"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue bandwidth by [[by]]","type":"timeseries"},{"class":"iops_panel","datasource":"Prometheus","description":"The rate of io queue operation\n\nsscylla_io_queue_total_operations","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"iops"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":15},"id":24,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"classes":{"selected":false,"text":"background_reclaim","value":"background_reclaim"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue IOPS by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":21},"id":25,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"classes":{"selected":false,"text":"background_reclaim","value":"background_reclaim"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"Disk $classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":21},"id":26,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"classes":{"selected":false,"text":"background_reclaim","value":"background_reclaim"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes Queue length by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"The time the class waited for being dispatched with non-empty software queue.\n\nLarge IO delays coupled with small starvation time denotes that scheduler is doing its job properly, and it's upper layer that overflows disk capacity.\n\nLarge IO delays coupled with large starvation time denotes that there might be some problem on the scheduler level that it cannot deliver IO requests from that class into disk in timely manner or the disk is slow and cannot afford timely dispatching.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":21},"id":27,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"classes":{"selected":false,"text":"background_reclaim","value":"background_reclaim"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes starvation time by [[by]]","type":"timeseries"},{"collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":27},"id":61,"panels":[],"repeatIteration":1728458975884,"repeatPanelId":20,"scopedVars":{"classes":{"selected":false,"text":"commitlog","value":"commitlog"}},"title":"$classes","type":"row"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":28},"id":62,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":21,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"commitlog","value":"commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":28},"id":63,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":22,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"commitlog","value":"commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes Queue length by [[by]]","type":"timeseries"},{"class":"bps_panel","datasource":"Prometheus","description":"The queue bandwidth rate in bytes \n\nscylla_io_queue_total_bytes","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":28},"id":64,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":23,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"commitlog","value":"commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue bandwidth by [[by]]","type":"timeseries"},{"class":"iops_panel","datasource":"Prometheus","description":"The rate of io queue operation\n\nsscylla_io_queue_total_operations","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"iops"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":28},"id":65,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":24,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"commitlog","value":"commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue IOPS by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":34},"id":66,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":25,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"commitlog","value":"commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"Disk $classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":34},"id":67,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":26,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"commitlog","value":"commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes Queue length by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"The time the class waited for being dispatched with non-empty software queue.\n\nLarge IO delays coupled with small starvation time denotes that scheduler is doing its job properly, and it's upper layer that overflows disk capacity.\n\nLarge IO delays coupled with large starvation time denotes that there might be some problem on the scheduler level that it cannot deliver IO requests from that class into disk in timely manner or the disk is slow and cannot afford timely dispatching.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":34},"id":68,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":27,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"commitlog","value":"commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes starvation time by [[by]]","type":"timeseries"},{"collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":40},"id":69,"panels":[],"repeatIteration":1728458975884,"repeatPanelId":20,"scopedVars":{"classes":{"selected":false,"text":"compaction","value":"compaction"}},"title":"$classes","type":"row"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":41},"id":70,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":21,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"compaction","value":"compaction"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":41},"id":71,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":22,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"compaction","value":"compaction"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes Queue length by [[by]]","type":"timeseries"},{"class":"bps_panel","datasource":"Prometheus","description":"The queue bandwidth rate in bytes \n\nscylla_io_queue_total_bytes","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":41},"id":72,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":23,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"compaction","value":"compaction"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue bandwidth by [[by]]","type":"timeseries"},{"class":"iops_panel","datasource":"Prometheus","description":"The rate of io queue operation\n\nsscylla_io_queue_total_operations","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"iops"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":41},"id":73,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":24,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"compaction","value":"compaction"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue IOPS by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":47},"id":74,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":25,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"compaction","value":"compaction"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"Disk $classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":47},"id":75,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":26,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"compaction","value":"compaction"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes Queue length by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"The time the class waited for being dispatched with non-empty software queue.\n\nLarge IO delays coupled with small starvation time denotes that scheduler is doing its job properly, and it's upper layer that overflows disk capacity.\n\nLarge IO delays coupled with large starvation time denotes that there might be some problem on the scheduler level that it cannot deliver IO requests from that class into disk in timely manner or the disk is slow and cannot afford timely dispatching.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":47},"id":76,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":27,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"compaction","value":"compaction"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes starvation time by [[by]]","type":"timeseries"},{"collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":53},"id":77,"panels":[],"repeatIteration":1728458975884,"repeatPanelId":20,"scopedVars":{"classes":{"selected":false,"text":"gossip","value":"gossip"}},"title":"$classes","type":"row"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":54},"id":78,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":21,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"gossip","value":"gossip"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":54},"id":79,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":22,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"gossip","value":"gossip"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes Queue length by [[by]]","type":"timeseries"},{"class":"bps_panel","datasource":"Prometheus","description":"The queue bandwidth rate in bytes \n\nscylla_io_queue_total_bytes","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":54},"id":80,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":23,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"gossip","value":"gossip"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue bandwidth by [[by]]","type":"timeseries"},{"class":"iops_panel","datasource":"Prometheus","description":"The rate of io queue operation\n\nsscylla_io_queue_total_operations","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"iops"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":54},"id":81,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":24,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"gossip","value":"gossip"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue IOPS by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":60},"id":82,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":25,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"gossip","value":"gossip"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"Disk $classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":60},"id":83,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":26,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"gossip","value":"gossip"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes Queue length by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"The time the class waited for being dispatched with non-empty software queue.\n\nLarge IO delays coupled with small starvation time denotes that scheduler is doing its job properly, and it's upper layer that overflows disk capacity.\n\nLarge IO delays coupled with large starvation time denotes that there might be some problem on the scheduler level that it cannot deliver IO requests from that class into disk in timely manner or the disk is slow and cannot afford timely dispatching.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":60},"id":84,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":27,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"gossip","value":"gossip"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes starvation time by [[by]]","type":"timeseries"},{"collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":66},"id":85,"panels":[],"repeatIteration":1728458975884,"repeatPanelId":20,"scopedVars":{"classes":{"selected":false,"text":"main","value":"main"}},"title":"$classes","type":"row"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":67},"id":86,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":21,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"main","value":"main"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":67},"id":87,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":22,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"main","value":"main"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes Queue length by [[by]]","type":"timeseries"},{"class":"bps_panel","datasource":"Prometheus","description":"The queue bandwidth rate in bytes \n\nscylla_io_queue_total_bytes","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":67},"id":88,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":23,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"main","value":"main"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue bandwidth by [[by]]","type":"timeseries"},{"class":"iops_panel","datasource":"Prometheus","description":"The rate of io queue operation\n\nsscylla_io_queue_total_operations","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"iops"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":67},"id":89,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":24,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"main","value":"main"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue IOPS by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":73},"id":90,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":25,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"main","value":"main"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"Disk $classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":73},"id":91,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":26,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"main","value":"main"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes Queue length by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"The time the class waited for being dispatched with non-empty software queue.\n\nLarge IO delays coupled with small starvation time denotes that scheduler is doing its job properly, and it's upper layer that overflows disk capacity.\n\nLarge IO delays coupled with large starvation time denotes that there might be some problem on the scheduler level that it cannot deliver IO requests from that class into disk in timely manner or the disk is slow and cannot afford timely dispatching.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":73},"id":92,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":27,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"main","value":"main"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes starvation time by [[by]]","type":"timeseries"},{"collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":79},"id":93,"panels":[],"repeatIteration":1728458975884,"repeatPanelId":20,"scopedVars":{"classes":{"selected":false,"text":"memtable","value":"memtable"}},"title":"$classes","type":"row"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":80},"id":94,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":21,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"memtable","value":"memtable"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":80},"id":95,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":22,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"memtable","value":"memtable"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes Queue length by [[by]]","type":"timeseries"},{"class":"bps_panel","datasource":"Prometheus","description":"The queue bandwidth rate in bytes \n\nscylla_io_queue_total_bytes","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":80},"id":96,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":23,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"memtable","value":"memtable"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue bandwidth by [[by]]","type":"timeseries"},{"class":"iops_panel","datasource":"Prometheus","description":"The rate of io queue operation\n\nsscylla_io_queue_total_operations","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"iops"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":80},"id":97,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":24,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"memtable","value":"memtable"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue IOPS by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":86},"id":98,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":25,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"memtable","value":"memtable"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"Disk $classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":86},"id":99,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":26,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"memtable","value":"memtable"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes Queue length by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"The time the class waited for being dispatched with non-empty software queue.\n\nLarge IO delays coupled with small starvation time denotes that scheduler is doing its job properly, and it's upper layer that overflows disk capacity.\n\nLarge IO delays coupled with large starvation time denotes that there might be some problem on the scheduler level that it cannot deliver IO requests from that class into disk in timely manner or the disk is slow and cannot afford timely dispatching.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":86},"id":100,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":27,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"memtable","value":"memtable"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes starvation time by [[by]]","type":"timeseries"},{"collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":92},"id":101,"panels":[],"repeatIteration":1728458975884,"repeatPanelId":20,"scopedVars":{"classes":{"selected":false,"text":"schema_commitlog","value":"schema_commitlog"}},"title":"$classes","type":"row"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":93},"id":102,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":21,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"schema_commitlog","value":"schema_commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":93},"id":103,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":22,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"schema_commitlog","value":"schema_commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes Queue length by [[by]]","type":"timeseries"},{"class":"bps_panel","datasource":"Prometheus","description":"The queue bandwidth rate in bytes \n\nscylla_io_queue_total_bytes","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":93},"id":104,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":23,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"schema_commitlog","value":"schema_commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue bandwidth by [[by]]","type":"timeseries"},{"class":"iops_panel","datasource":"Prometheus","description":"The rate of io queue operation\n\nsscylla_io_queue_total_operations","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"iops"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":93},"id":105,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":24,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"schema_commitlog","value":"schema_commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue IOPS by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":99},"id":106,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":25,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"schema_commitlog","value":"schema_commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"Disk $classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":99},"id":107,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":26,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"schema_commitlog","value":"schema_commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes Queue length by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"The time the class waited for being dispatched with non-empty software queue.\n\nLarge IO delays coupled with small starvation time denotes that scheduler is doing its job properly, and it's upper layer that overflows disk capacity.\n\nLarge IO delays coupled with large starvation time denotes that there might be some problem on the scheduler level that it cannot deliver IO requests from that class into disk in timely manner or the disk is slow and cannot afford timely dispatching.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":99},"id":108,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":27,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"schema_commitlog","value":"schema_commitlog"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes starvation time by [[by]]","type":"timeseries"},{"collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":105},"id":109,"panels":[],"repeatIteration":1728458975884,"repeatPanelId":20,"scopedVars":{"classes":{"selected":false,"text":"statement","value":"statement"}},"title":"$classes","type":"row"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":106},"id":110,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":21,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":106},"id":111,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":22,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes Queue length by [[by]]","type":"timeseries"},{"class":"bps_panel","datasource":"Prometheus","description":"The queue bandwidth rate in bytes \n\nscylla_io_queue_total_bytes","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":106},"id":112,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":23,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue bandwidth by [[by]]","type":"timeseries"},{"class":"iops_panel","datasource":"Prometheus","description":"The rate of io queue operation\n\nsscylla_io_queue_total_operations","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"iops"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":106},"id":113,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":24,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue IOPS by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":112},"id":114,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":25,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"Disk $classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":112},"id":115,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":26,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes Queue length by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"The time the class waited for being dispatched with non-empty software queue.\n\nLarge IO delays coupled with small starvation time denotes that scheduler is doing its job properly, and it's upper layer that overflows disk capacity.\n\nLarge IO delays coupled with large starvation time denotes that there might be some problem on the scheduler level that it cannot deliver IO requests from that class into disk in timely manner or the disk is slow and cannot afford timely dispatching.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":112},"id":116,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":27,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes starvation time by [[by]]","type":"timeseries"},{"collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":118},"id":117,"panels":[],"repeatIteration":1728458975884,"repeatPanelId":20,"scopedVars":{"classes":{"selected":false,"text":"streaming","value":"streaming"}},"title":"$classes","type":"row"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":119},"id":118,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":21,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"streaming","value":"streaming"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_delay_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the queue","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":119},"id":119,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":22,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"streaming","value":"streaming"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes Queue length by [[by]]","type":"timeseries"},{"class":"bps_panel","datasource":"Prometheus","description":"The queue bandwidth rate in bytes \n\nscylla_io_queue_total_bytes","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":119},"id":120,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":23,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"streaming","value":"streaming"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_bytes{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue bandwidth by [[by]]","type":"timeseries"},{"class":"iops_panel","datasource":"Prometheus","description":"The rate of io queue operation\n\nsscylla_io_queue_total_operations","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"iops"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":119},"id":121,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":24,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"streaming","value":"streaming"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"$classes I/O Queue IOPS by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"Total time spent in disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":125},"id":122,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":25,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"streaming","value":"streaming"}},"span":3,"targets":[{"expr":"topk([[topk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(rate(scylla_io_queue_total_exec_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])/rate(scylla_io_queue_total_operations{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() max(scylla_io_queue_delay{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"Disk $classes I/O Queue delay by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of requests in the disk","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":125},"id":123,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":26,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"streaming","value":"streaming"}},"span":3,"targets":[{"expr":"topk([[topk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], max(scylla_io_queue_disk_queue_length{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes Queue length by [[by]]","type":"timeseries"},{"class":"seconds_panel","datasource":"Prometheus","description":"The time the class waited for being dispatched with non-empty software queue.\n\nLarge IO delays coupled with small starvation time denotes that scheduler is doing its job properly, and it's upper layer that overflows disk capacity.\n\nLarge IO delays coupled with large starvation time denotes that there might be some problem on the scheduler level that it cannot deliver IO requests from that class into disk in timely manner or the disk is slow and cannot afford timely dispatching.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":125},"id":124,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"repeatIteration":1728458975884,"repeatPanelId":27,"repeatedByRow":true,"scopedVars":{"classes":{"selected":false,"text":"streaming","value":"streaming"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_io_queue_starvation_time_sec{class=\"$classes\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"refId":"A","step":30}],"title":"DISK $classes starvation time by [[by]]","type":"timeseries"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":131},"id":28,"panels":[],"repeat":"","title":"","type":"row"},{"class":"plain_text","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":2,"w":24,"x":0,"y":132},"id":29,"isNew":true,"links":[],"options":{"content":"<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">Information by Task Group</h1>","mode":"html"},"pluginVersion":"7.4.2","span":12,"style":{},"title":"","transparent":true,"type":"text"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":134},"id":35,"panels":[],"repeat":"","title":"Internal node errors","type":"row"},{"class":"plain_text","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":2,"w":24,"x":0,"y":135},"id":36,"isNew":true,"links":[],"options":{"content":"<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">Internal node Errors - $cluster</h1>","mode":"html"},"pluginVersion":"7.4.2","span":12,"style":{},"title":"","transparent":true,"type":"text"},{"class":"ops_panel","datasource":"Prometheus","description":"Number of AIO Errors","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":8,"x":0,"y":137},"id":43,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":4,"targets":[{"expr":"topk([[topk]], avg(rate(scylla_reactor_aio_errors{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], avg(rate(scylla_reactor_aio_errors{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"AIO Error by [[by]]","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"Total number of abandoned failed futures, futures destroyed while still containing an exception.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":8,"x":8,"y":137},"id":44,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":4,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_reactor_abandoned_failed_futures{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_reactor_abandoned_failed_futures{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"Ignored Future By [[by]]","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"Number of C++ exceptions thrown.\n\n An exception by itself does not indicate a problem","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":8,"x":16,"y":137},"id":45,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":4,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_reactor_cpp_exceptions{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_reactor_cpp_exceptions{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"C++ Exceptions [[by]]","type":"timeseries"},{"class":"text_panel","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":6,"w":8,"x":16,"y":143},"id":39,"isNew":true,"links":[],"options":{"content":"##  ","mode":"markdown"},"pluginVersion":"7.4.2","span":4,"style":{},"title":"","transparent":true,"type":"text"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":149},"id":46,"panels":[],"repeat":"","title":"Commit Log","type":"row"},{"class":"plain_text","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":2,"w":24,"x":0,"y":150},"id":47,"isNew":true,"links":[],"options":{"content":"<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">Commit log Information</h1>","mode":"html"},"pluginVersion":"7.4.2","span":12,"style":{},"title":"","transparent":true,"type":"text"},{"class":"bytes_panel","datasource":"Prometheus","description":"Holds the size of disk space in bytes reserved for data so far. A too high value indicates that we have some bottleneck in the writing to sstables path","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":152},"id":48,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], avg(scylla_commitlog_disk_total_bytes{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], avg(scylla_commitlog_disk_total_bytes{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"Avg reserved disk space by [[by]]","type":"timeseries"},{"class":"bytes_panel","datasource":"Prometheus","description":"Holds the size of disk space in bytes used for data so far. A too high value indicates that we have some bottleneck in the writing to sstables path","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":152},"id":49,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], avg(scylla_commitlog_disk_active_bytes{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], avg(scylla_commitlog_disk_active_bytes{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"Avg used disk space by [[by]]","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"Counts a number of times the flush() method was called for a file","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":152},"id":50,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], avg(rate(scylla_commitlog_flush{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], avg(rate(scylla_commitlog_flush{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"Avg flush by [[by]]","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"Holds the current number of segments","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":152},"id":51,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], avg(scylla_commitlog_segments{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], avg(scylla_commitlog_segments{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"Segments by [[by]]","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"Counts a number of times a flush limit was exceeded. A non-zero value indicates that there are too many pending flush operations (see pending_flushes) and some of them will be blocked till the total amount of pending flush operations drops below 5.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":158},"id":52,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], avg(rate(scylla_commitlog_flush_limit_exceeded{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], avg(rate(scylla_commitlog_flush_limit_exceeded{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"Avg flush limit exceeded by [[by]]","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"Holds the number of currently pending allocations. A non-zero value indicates that we have a bottleneck in the disk write flow.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":158},"id":53,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], avg(scylla_commitlog_pending_allocations{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], avg(scylla_commitlog_pending_allocations{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"Pending allocations by [[by]]","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"Counts a number of requests blocked due to memory pressure. A non-zero value indicates that the commitlog memory quota is not enough to serve the required amount of requests.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":158},"id":54,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], avg(scylla_commitlog_pending_flushes{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], avg(scylla_commitlog_pending_flushes{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"Pending flush by [[by]]","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"Holds the current number of unused segments. A non-zero value indicates that the disk write path became temporary slow.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":158},"id":55,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], avg(scylla_commitlog_unused_segments{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], avg(scylla_commitlog_unused_segments{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"Unused segments by [[by]]","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"Holds the number of not closed segments that still have some free space. This value should not get too high.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":164},"id":56,"isNew":true,"links":[],"options":{"class":"desc_tooltip_options","legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], avg(scylla_commitlog_allocating_segments{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], avg(scylla_commitlog_allocating_segments{instance=~\"[[node]]\" ,cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":30}],"title":"Allocating segments by [[by]]","type":"timeseries"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":170},"id":57,"panels":[],"repeat":"","title":"RPC metrics","type":"row"},{"class":"graph_panel","datasource":"Prometheus","description":"scylla_rpc_client_count","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":4,"x":0,"y":171},"id":58,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":2,"targets":[{"expr":"topk([[topk]], sum(scylla_rpc_client_count{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}) by ([[by]],domain)>0) or on ([[by]],domain) bottomk([[bottomk]], sum(scylla_rpc_client_count{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}) by ([[by]],domain)>0)","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"RPC Client count by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"scylla_rpc_client_sent_messages","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":4,"x":4,"y":171},"id":59,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":2,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_rpc_client_sent_messages{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])>0) by ([[by]],domain)) or on ([[by]],domain) bottomk([[bottomk]], sum(rate(scylla_rpc_client_sent_messages{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]],domain)>0)","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"RPC sent messages by [[by]]","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"scylla_rpc_client_replied","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":4,"x":8,"y":171},"id":60,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":2,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_rpc_client_replied{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])>0) by ([[by]],domain)) or on ([[by]],domain) bottomk([[bottomk]], sum(rate(scylla_rpc_client_replied{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]],domain)>0)","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"RPC replied messages by [[by]]","type":"timeseries"}],"refresh":"30s","schemaVersion":27,"style":"dark","tags":["6.0.1"],"templating":{"list":[{"allValue":null,"current":{"tags":[],"text":"Instance","value":"instance"},"description":null,"error":null,"hide":0,"includeAll":false,"label":"by","multi":false,"name":"by","options":[{"selected":false,"text":"Cluster","value":"cluster"},{"selected":false,"text":"DC","value":"dc"},{"selected":true,"text":"Instance","value":"instance"},{"selected":false,"text":"Shard","value":"instance,shard"}],"query":"Instance : instance,Cluster : cluster,DC : dc,Shard : instance\\,shard","skipUrlSync":false,"type":"custom"},{"allValue":null,"class":"template_variable_single","current":{"selected":false,"text":"EGT_ScyllaDB_Cluster_DEV","value":"EGT_ScyllaDB_Cluster_DEV"},"datasource":"Prometheus","definition":"label_values(scylla_reactor_utilization, cluster)","description":null,"error":null,"hide":0,"includeAll":false,"label":"cluster","multi":false,"name":"cluster","options":[],"query":{"query":"label_values(scylla_reactor_utilization, cluster)","refId":"StandardVariableQuery"},"refresh":2,"regex":"","skipUrlSync":false,"sort":1,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":".*","class":"template_variable_all","current":{"selected":true,"text":["All"],"value":["$__all"]},"datasource":"Prometheus","definition":"label_values(scylla_reactor_utilization{cluster=~\"$cluster\"}, dc)","description":null,"error":null,"hide":0,"includeAll":true,"label":"dc","multi":true,"name":"dc","options":[],"query":{"query":"label_values(scylla_reactor_utilization{cluster=~\"$cluster\"}, dc)","refId":"StandardVariableQuery"},"refresh":2,"regex":"","skipUrlSync":false,"sort":1,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":".*","class":"template_variable_all","current":{"selected":true,"text":["All"],"value":["$__all"]},"datasource":"Prometheus","definition":"label_values(scylla_reactor_utilization{cluster=\"$cluster\", dc=~\"$dc\"}, instance)","description":null,"error":null,"hide":0,"includeAll":true,"label":"node","multi":true,"name":"node","options":[],"query":{"query":"label_values(scylla_reactor_utilization{cluster=\"$cluster\", dc=~\"$dc\"}, instance)","refId":"StandardVariableQuery"},"refresh":2,"regex":"","skipUrlSync":false,"sort":1,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":".*","class":"template_variable_all","current":{"selected":true,"text":["All"],"value":["$__all"]},"datasource":"Prometheus","definition":"label_values(scylla_reactor_utilization,shard)","description":null,"error":null,"hide":0,"includeAll":true,"label":"shard","multi":true,"name":"shard","options":[],"query":{"query":"label_values(scylla_reactor_utilization,shard)","refId":"StandardVariableQuery"},"refresh":2,"regex":"","skipUrlSync":false,"sort":3,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":".*","class":"template_variable_all","current":{"selected":true,"text":["All"],"value":["$__all"]},"datasource":"Prometheus","definition":"label_values(scylla_io_queue_delay,class)","description":null,"error":null,"hide":0,"includeAll":true,"label":"classes","multi":true,"name":"classes","options":[],"query":{"query":"label_values(scylla_io_queue_delay,class)","refId":"StandardVariableQuery"},"refresh":2,"regex":"","skipUrlSync":false,"sort":1,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":".*","class":"template_variable_all","current":{"selected":true,"text":["All"],"value":["$__all"]},"datasource":"Prometheus","definition":"label_values(scylla_scheduler_time_spent_on_task_quota_violations_ms,group)","description":null,"error":null,"hide":0,"includeAll":true,"label":"group","multi":true,"name":"sg","options":[],"query":{"query":"label_values(scylla_scheduler_time_spent_on_task_quota_violations_ms,group)","refId":"StandardVariableQuery"},"refresh":2,"regex":"","skipUrlSync":false,"sort":1,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":".*","class":"template_variable_all","current":{"selected":true,"text":["All"],"value":["$__all"]},"dashversion":[">5.4",">2024.1"],"datasource":"Prometheus","definition":"label_values(scylla_io_queue_consumption,iogroup)","description":null,"error":null,"hide":0,"includeAll":true,"label":"iogroup","multi":true,"name":"iogroup","options":[],"query":{"query":"label_values(scylla_io_queue_consumption,iogroup)","refId":"StandardVariableQuery"},"refresh":2,"regex":"","skipUrlSync":false,"sort":1,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":null,"class":"aggregation_function","current":{"tags":[],"text":"avg","value":"avg"},"description":null,"error":null,"hide":0,"includeAll":false,"label":"Function","multi":false,"name":"func","options":[{"selected":true,"text":"sum","value":"sum"},{"selected":false,"text":"avg","value":"avg"},{"selected":false,"text":"max","value":"max"},{"selected":false,"text":"min","value":"min"},{"selected":false,"text":"stddev","value":"stddev"},{"selected":false,"text":"stdvar","value":"stdvar"}],"query":"sum,avg,max,min,stddev,stdvar","skipUrlSync":false,"type":"custom"},{"allValue":null,"class":"topk_limit","current":{"tags":[],"text":"256","value":"256"},"description":null,"error":null,"hide":0,"includeAll":false,"label":"Filter Highest","multi":false,"name":"topk","options":[{"selected":false,"text":"0","value":"0"},{"selected":false,"text":"5","value":"5"},{"selected":false,"text":"10","value":"10"},{"selected":false,"text":"20","value":"20"},{"selected":false,"text":"50","value":"50"},{"selected":false,"text":"100","value":"100"},{"selected":true,"text":"256","value":"256"},{"selected":false,"text":"512","value":"512"},{"selected":false,"text":"1000","value":"1000"},{"selected":false,"text":"10000","value":"10000"}],"query":"0, 5, 10, 20, 50, 100, 256, 500, 1000, 10000","skipUrlSync":false,"type":"custom"},{"allValue":null,"class":"botomk_limit","current":{"tags":[],"text":"0","value":"0"},"description":null,"error":null,"hide":0,"includeAll":false,"label":"Filter Lowest","multi":false,"name":"bottomk","options":[{"selected":true,"text":"0","value":"0"},{"selected":false,"text":"5","value":"5"},{"selected":false,"text":"10","value":"10"},{"selected":false,"text":"20","value":"20"},{"selected":false,"text":"50","value":"50"},{"selected":false,"text":"100","value":"100"},{"selected":true,"text":"256","value":"256"},{"selected":false,"text":"512","value":"512"},{"selected":false,"text":"1000","value":"1000"},{"selected":false,"text":"10000","value":"10000"}],"query":"0, 5, 10, 20, 50, 100, 256, 500, 1000, 10000","skipUrlSync":false,"type":"custom"},{"allValue":null,"class":"template_variable_custom","current":{"text":"6.0.1","value":"6.0.1"},"description":null,"error":null,"hide":2,"includeAll":false,"label":null,"multi":false,"name":"scylla_version","options":[{"selected":true,"text":"6.0.1","value":"6.0.1"}],"query":"6.0.1","skipUrlSync":false,"type":"custom"},{"allValue":null,"class":"monitor_version_var","current":{"text":"master","value":"master"},"description":null,"error":null,"hide":2,"includeAll":false,"label":null,"multi":false,"name":"monitoring_version","options":[{"selected":true,"text":"master","value":"master"}],"query":"master","skipUrlSync":false,"type":"custom"}]},"time":{"from":"now-30m","to":"now"},"timepicker":{"now":true,"refresh_intervals":["5s","10s","30s","1m","5m","15m","30m","1h","2h","1d"],"time_options":["5m","15m","1h","6h","12h","24h","2d","7d","30d"]},"timezone":"utc","title":"ScyllaDB Advanced","uid":"advanced-6-0-1","version":1}