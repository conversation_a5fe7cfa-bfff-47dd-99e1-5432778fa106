{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Unified ClickHouse Queries Dashboard - Helps to visualize most frequent, slowest, failed queries.\r\nShows queries rate per second, table with last queries. Switch between DEV/INT/UAT environments using the datasource variable.", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "title": "Top charts", "type": "row"}, {"datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.4.0", "targets": [{"datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${datasource}"}, "dateTimeType": "DATETIME", "extrapolate": true, "format": "time_series", "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t", "intervalFactor": 1, "query": "SELECT\n    $timeSeries as t,\n    count() as queries_count\nFROM system.query_log\nWHERE $timeFilter\n    AND type IN (${type:singlequote})\n    AND initial_user IN (${user:singlequote})\n    AND if('${query_type}' = 'all', 1=1, if('${query_type}' = 'select', query_kind = 'Select', query_kind != 'Select'))\nGROUP BY t\nORDER BY t", "rawQuery": "SELECT\n    $timeSeries as t,\n    count() as queries_count\nFROM system.query_log\nWHERE $timeFilter\n    AND type IN (${type:singlequote})\n    AND initial_user IN (${user:singlequote})\n    AND if('${query_type}' = 'all', 1=1, if('${query_type}' = 'select', query_kind = 'Select', query_kind != 'Select'))\nGROUP BY t\nORDER BY t", "refId": "A", "round": "0s", "skip_comments": true}], "title": "Queries rate per second", "type": "timeseries"}, {"datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.4.0", "targets": [{"datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${datasource}"}, "dateTimeType": "DATETIME", "extrapolate": true, "format": "time_series", "intervalFactor": 1, "query": "SELECT\n    $timeSeries as t,\n    avg(query_duration_ms) as avg_duration\nFROM system.query_log\nWHERE $timeFilter\n    AND type IN (${type:singlequote})\n    AND initial_user IN (${user:singlequote})\n    AND if('${query_type}' = 'all', 1=1, if('${query_type}' = 'select', query_kind = 'Select', query_kind != 'Select'))\nGROUP BY t\nORDER BY t", "rawQuery": "SELECT\n    $timeSeries as t,\n    avg(query_duration_ms) as avg_duration\nFROM system.query_log\nWHERE $timeFilter\n    AND type IN (${type:singlequote})\n    AND initial_user IN (${user:singlequote})\n    AND if('${query_type}' = 'all', 1=1, if('${query_type}' = 'select', query_kind = 'Select', query_kind != 'Select'))\nGROUP BY t\nORDER BY t", "refId": "A", "round": "0s", "skip_comments": true}], "title": "Average Query Duration", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 40, "tags": ["clickhouse", "performance", "unified"], "templating": {"list": [{"current": {"selected": false, "text": "DEV", "value": "MSgHPxPIk"}, "hide": 0, "includeAll": false, "label": "Environment", "multi": false, "name": "datasource", "options": [{"selected": true, "text": "DEV", "value": "MSgHPxPIk"}, {"selected": false, "text": "INT", "value": "FSn_t7RNz"}, {"selected": false, "text": "UAT", "value": "cef0o5s7s6z9ce"}], "query": "MSgHPxPIk : DEV, FSn_t7RNz : INT, cef0o5s7s6z9ce : UAT", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"auto": true, "auto_count": 100, "auto_min": "1m", "current": {"text": "5m", "value": "5m"}, "hide": 2, "name": "interval", "options": [{"selected": false, "text": "auto", "value": "$__auto_interval_interval"}, {"selected": true, "text": "5m", "value": "5m"}], "query": "5m", "refresh": 2, "type": "interval"}, {"current": {"tags": [], "text": ["All"], "value": ["$__all"]}, "includeAll": true, "label": "type", "multi": true, "name": "type", "options": [{"selected": true, "text": "All", "value": "$__all"}, {"selected": false, "text": "1", "value": "1"}, {"selected": false, "text": "2", "value": "2"}, {"selected": false, "text": "3", "value": "3"}, {"selected": false, "text": "4", "value": "4"}], "query": "1,2,3,4", "type": "custom"}, {"current": {"tags": [], "text": "5", "value": "5"}, "includeAll": false, "label": "top elements", "name": "top", "options": [{"selected": true, "text": "5", "value": "5"}, {"selected": false, "text": "10", "value": "10"}, {"selected": false, "text": "15", "value": "15"}, {"selected": false, "text": "20", "value": "20"}, {"selected": false, "text": "25", "value": "25"}, {"selected": false, "text": "30", "value": "30"}], "query": "5,10,15,20,25,30", "type": "custom"}, {"current": {"text": "All", "value": "$__all"}, "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${datasource}"}, "definition": "", "includeAll": true, "label": "initial user", "multi": true, "name": "user", "options": [], "query": "select distinct initial_user from system.query_log where event_date >= today()-3 and event_time > toDateTime(today()-3)", "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "all", "value": "all"}, "includeAll": false, "label": "query type", "name": "query_type", "options": [{"selected": true, "text": "all", "value": "all"}, {"selected": false, "text": "select", "value": "select"}, {"selected": false, "text": "insert", "value": "insert"}], "query": "all,select,insert", "type": "custom"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "ClickHouse Queries - Unified (DEV/INT/UAT)", "uid": "clickhouse-queries-unified", "version": 1, "weekStart": ""}