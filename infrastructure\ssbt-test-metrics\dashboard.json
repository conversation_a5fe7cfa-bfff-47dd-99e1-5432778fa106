{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 706, "links": [], "panels": [{"cacheTimeout": null, "columns": [], "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0}, "id": 4, "links": [], "pageSize": null, "pluginVersion": "6.3.6", "scroll": false, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"$$hashKey": "object:2168", "alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "hidden"}, {"$$hashKey": "object:2169", "alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "node_uname_info{instance=~\"*************:9100\"}", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "", "transform": "timeseries_aggregations", "type": "table-old"}, {"cacheTimeout": null, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}, {"color": "dark-red", "value": 19.5}]}, "unit": "percent"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "{endpoint=\"metrics\", instance=\"*************:9100\", job=\"ews-ssbt-cachier-test\", namespace=\"ews-dev\", service=\"ews-ssbt-cachier-test\"}"}, "properties": [{"id": "displayName", "value": "EWS-SSBT-Terminal-Office active memory : "}, {"id": "unit", "value": "decgbytes"}]}]}, "gridPos": {"h": 4, "w": 10, "x": 0, "y": 3}, "hideTimeOverride": true, "id": 2, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {"valueSize": 27}, "textMode": "value_and_name"}, "pluginVersion": "7.4.2", "targets": [{"expr": "(((node_memory_MemTotal_bytes{instance=\"*************:9100\"} - node_memory_MemFree_bytes{instance=\"*************:9100\"})) / node_memory_MemTotal_bytes{instance=\"*************:9100\"} ) * 100", "hide": false, "interval": "", "legendFormat": "Active memory in percentile is  -- ", "refId": "C"}, {"expr": "node_memory_Active_bytes{instance=\"*************:9100\"} / (1024*1024*1024)", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": "1m", "timeShift": null, "title": "Memory Usage", "transformations": [], "type": "stat"}, {"cacheTimeout": null, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 0.3}, {"color": "dark-red", "value": 19.7}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 7, "x": 10, "y": 3}, "hideTimeOverride": true, "id": 5, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {"valueSize": 27}, "textMode": "value_and_name"}, "pluginVersion": "7.4.2", "targets": [{"expr": "avg(node_load1{instance=~\"*************:9100\"}) /  count(count(node_cpu_seconds_total{instance=~\"*************:9100\"}) by (cpu)) * 100", "hide": false, "interval": "", "legendFormat": "Average CPU Usage is ", "refId": "C"}], "timeFrom": "1m", "timeShift": null, "title": "CPU Usage", "transformations": [], "type": "stat"}, {"cacheTimeout": null, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 0.3}, {"color": "dark-red", "value": 19.7}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 7, "x": 17, "y": 3}, "hideTimeOverride": true, "id": 8, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {"valueSize": 27}, "textMode": "value_and_name"}, "pluginVersion": "7.4.2", "targets": [{"expr": "((sum(node_filesystem_free_bytes{instance=~\"*************:9100\"}) / sum(node_filesystem_size_bytes{instance=~\"*************:9100\"}) )) * 100", "hide": false, "interval": "", "legendFormat": "HDD Usage is ", "refId": "C"}], "timeFrom": "1m", "timeShift": null, "title": "Disk Usage", "transformations": [], "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "up{endpoint=\"metrics\", instance=\"*************:9100\", job=\"ews-ssbt-cachier-test\", namespace=\"ews-dev\", service=\"ews-ssbt-cachier-test\"}"}, "properties": [{"id": "displayName", "value": "Is the terminal up ?"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, {"id": "mappings", "value": [{"from": "", "id": 1, "text": "Yes", "to": "", "type": 1, "value": "1"}, {"from": "", "id": 2, "text": "No", "to": "", "type": 1, "value": "0"}]}]}]}, "gridPos": {"h": 5, "w": 24, "x": 0, "y": 7}, "id": 7, "options": {"showHeader": true}, "pluginVersion": "7.4.2", "targets": [{"expr": "up{instance=\"*************:9100\"} == 1", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Is it Up ?", "type": "table"}], "refresh": "30s", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "SSBT Terminal Metrics", "uid": "nullasdas123asx12acsq24gjsl", "version": 3}