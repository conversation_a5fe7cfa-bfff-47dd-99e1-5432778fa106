{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Performance metrics for Postgres", "editable": true, "gnetId": 455, "graphTooltip": 0, "id": 48, "iteration": 1622641424695, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 20, "x": 0, "y": 0}, "hiddenSeries": false, "id": 1, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "fetched", "dsType": "prometheus", "expr": "sum(irate(pg_stat_database_tup_fetched{datname=~\"$db\",instance=~\"$host\"}[5m]))", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "legendFormat": "fetched", "measurement": "postgresql", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["tup_fetched"], "type": "field"}, {"params": [], "type": "mean"}, {"params": ["10s"], "type": "non_negative_derivative"}]], "step": 120, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "fetched", "dsType": "prometheus", "expr": "sum(irate(pg_stat_database_tup_returned{datname=~\"$db\",instance=~\"$host\"}[5m]))", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "legendFormat": "returned", "measurement": "postgresql", "policy": "default", "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["tup_fetched"], "type": "field"}, {"params": [], "type": "mean"}, {"params": ["10s"], "type": "non_negative_derivative"}]], "step": 120, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "fetched", "dsType": "prometheus", "expr": "sum(irate(pg_stat_database_tup_inserted{datname=~\"$db\",instance=~\"$host\"}[5m]))", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "legendFormat": "inserted", "measurement": "postgresql", "policy": "default", "refId": "C", "resultFormat": "time_series", "select": [[{"params": ["tup_fetched"], "type": "field"}, {"params": [], "type": "mean"}, {"params": ["10s"], "type": "non_negative_derivative"}]], "step": 120, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "fetched", "dsType": "prometheus", "expr": "sum(irate(pg_stat_database_tup_updated{datname=~\"$db\",instance=~\"$host\"}[5m]))", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "legendFormat": "updated", "measurement": "postgresql", "policy": "default", "refId": "D", "resultFormat": "time_series", "select": [[{"params": ["tup_fetched"], "type": "field"}, {"params": [], "type": "mean"}, {"params": ["10s"], "type": "non_negative_derivative"}]], "step": 120, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "fetched", "dsType": "prometheus", "expr": "sum(irate(pg_stat_database_tup_deleted{datname=~\"$db\",instance=~\"$host\"}[5m]))", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "legendFormat": "deleted", "measurement": "postgresql", "policy": "default", "refId": "E", "resultFormat": "time_series", "select": [[{"params": ["tup_fetched"], "type": "field"}, {"params": [], "type": "mean"}, {"params": ["10s"], "type": "non_negative_derivative"}]], "step": 120, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Rows", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": {"type": "prometheus"}, "decimals": 0, "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 4, "x": 20, "y": 0}, "height": "55px", "id": 11, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": true, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"dsType": "prometheus", "expr": "sum(irate(pg_stat_database_xact_commit{datname=~\"$db\",instance=~\"$host\"}[5m])) + sum(irate(pg_stat_database_xact_rollback{datname=~\"$db\",instance=~\"$host\"}[5m]))", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "measurement": "postgresql", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["xact_commit"], "type": "field"}, {"params": [], "type": "mean"}, {"params": ["10s"], "type": "non_negative_derivative"}]], "step": 1800, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": "", "title": "QPS", "transparent": true, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "decimals": 1, "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 7}, "hiddenSeries": false, "id": 2, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideZero": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "Buff<PERSON> Allocated", "dsType": "prometheus", "expr": "irate(pg_stat_bgwriter_buffers_alloc{instance='$host'}[5m])", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "legendFormat": "buffers_alloc", "measurement": "postgresql", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["buffers_alloc"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [], "type": "difference"}]], "step": 240, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "Buff<PERSON> Allocated", "dsType": "prometheus", "expr": "irate(pg_stat_bgwriter_buffers_backend_fsync{instance='$host'}[5m])", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "legendFormat": "buffers_backend_fsync", "measurement": "postgresql", "policy": "default", "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["buffers_alloc"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [], "type": "difference"}]], "step": 240, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "Buff<PERSON> Allocated", "dsType": "prometheus", "expr": "irate(pg_stat_bgwriter_buffers_backend{instance='$host'}[5m])", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "legendFormat": "buffers_backend", "measurement": "postgresql", "policy": "default", "refId": "C", "resultFormat": "time_series", "select": [[{"params": ["buffers_alloc"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [], "type": "difference"}]], "step": 240, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "Buff<PERSON> Allocated", "dsType": "prometheus", "expr": "irate(pg_stat_bgwriter_buffers_clean{instance='$host'}[5m])", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "legendFormat": "buffers_clean", "measurement": "postgresql", "policy": "default", "refId": "D", "resultFormat": "time_series", "select": [[{"params": ["buffers_alloc"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [], "type": "difference"}]], "step": 240, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "Buff<PERSON> Allocated", "dsType": "prometheus", "expr": "irate(pg_stat_bgwriter_buffers_checkpoint{instance='$host'}[5m])", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "legendFormat": "buffers_checkpoint", "measurement": "postgresql", "policy": "default", "refId": "E", "resultFormat": "time_series", "select": [[{"params": ["buffers_alloc"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [], "type": "difference"}]], "step": 240, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 7}, "hiddenSeries": false, "id": 3, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "conflicts", "dsType": "prometheus", "expr": "sum(rate(pg_stat_database_deadlocks{datname=~\"$db\",instance=~\"$host\"}[5m]))", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "legendFormat": "deadlocks", "measurement": "postgresql", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["conflicts"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [], "type": "difference"}]], "step": 240, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "deadlocks", "dsType": "prometheus", "expr": "sum(rate(pg_stat_database_conflicts{datname=~\"$db\",instance=~\"$host\"}[5m]))", "format": "time_series", "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "intervalFactor": 2, "legendFormat": "conflicts", "measurement": "postgresql", "policy": "default", "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["deadlocks"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [], "type": "difference"}]], "step": 240, "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Conflicts/Deadlocks", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 14}, "hiddenSeries": false, "id": 12, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "7.4.2", "pointradius": 1, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(pg_stat_database_blks_hit{datname=~\"$db\",instance=~\"$host\"}) / (sum(pg_stat_database_blks_hit{datname=~\"$db\",instance=~\"$host\"}) + sum(pg_stat_database_blks_read{datname=~\"$db\",instance=~\"$host\"}))", "format": "time_series", "intervalFactor": 2, "legendFormat": "cache hit rate", "refId": "A", "step": 240}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Cache hit ratio", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 14}, "hiddenSeries": false, "id": 13, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "pg_stat_database_numbackends{datname=~\"$db\",instance=~\"$host\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{__name__}}", "refId": "A", "step": 240}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Number of active connections", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["postgres"], "templating": {"list": [{"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus"}, "definition": "", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": false, "name": "host", "options": [], "query": {"query": "label_values(up{job=~\"postgres.*\"},instance)", "refId": "Prometheus-host-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus"}, "definition": "", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "db", "multi": false, "name": "db", "options": [], "query": {"query": "label_values(pg_stat_database_tup_fetched{instance=~\"$host\",datname!~\"template.*|postgres\"},datname)", "refId": "Prometheus-db-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Postgres Overview", "uid": "wGgaPlcizetete", "version": 1}