# PowerShell script to add folderRef: infrastructure to all dashboard.yaml files
param(
    [string]$RootDir = "infrastructure"
)

# Get all dashboard.yaml files recursively
$dashboardFiles = Get-ChildItem -Path $RootDir -Recurse -Name "dashboard.yaml"

Write-Host "Found $($dashboardFiles.Count) dashboard.yaml files to update"

foreach ($file in $dashboardFiles) {
    $fullPath = Join-Path $RootDir $file
    Write-Host "Processing: $file"
    
    # Read the file content
    $content = Get-Content $fullPath -Raw
    
    # Check if folderRef already exists
    if ($content -match "folderRef:") {
        Write-Host "  - Already has folderRef, skipping"
        continue
    }
    
    # Split content into lines
    $lines = $content -split "`r?`n"
    $newLines = @()

    foreach ($line in $lines) {
        $newLines += $line
        if ($line -eq "spec:") {
            $newLines += "  folderRef: infrastructure"
        }
    }

    # Write back to file
    $newLines -join "`n" | Out-File -FilePath $fullPath -Encoding UTF8 -NoNewline
    
    Write-Host "  - Added folderRef: infrastructure"
}

Write-Host "Completed updating all dashboard.yaml files!"
