# PowerShell script to add folderRef: infrastructure to all dashboard.yaml files
param(
    [string]$RootDir = "infrastructure"
)

# Get all dashboard.yaml files recursively
$dashboardFiles = Get-ChildItem -Path $RootDir -Recurse -Name "dashboard.yaml"

Write-Host "Found $($dashboardFiles.Count) dashboard.yaml files to update"

foreach ($file in $dashboardFiles) {
    $fullPath = Join-Path $RootDir $file
    Write-Host "Processing: $file"
    
    # Read the file content
    $content = Get-Content $fullPath -Raw
    
    # Check if folderRef already exists
    if ($content -match "folderRef:") {
        Write-Host "  - Already has folderRef, skipping"
        continue
    }
    
    # Add folderRef after spec: line
    $updatedContent = $content -replace "spec:", "spec:`n  folderRef: infrastructure"
    
    # Write back to file
    $updatedContent | Out-File -FilePath $fullPath -Encoding UTF8 -NoNewline
    
    Write-Host "  - Added folderRef: infrastructure"
}

Write-Host "Completed updating all dashboard.yaml files!"
