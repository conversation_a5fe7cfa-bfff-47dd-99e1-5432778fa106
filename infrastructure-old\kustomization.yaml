apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- postgres-experimental
- clickhouse
- vault
- scylla
- redis-stream-queue-prometheus-sourced.yaml
- redis-exporter-dashboard.yaml
- redis-pending-msg.yaml
- redis-vm-hosts-dashboard.yaml
- host-metrics-dashboard.yaml
- redis-memory-utilization.yaml
- x509-certificate-exporter-dashboard.yaml
- rabbitmq-overview-devops.yaml
- strimzi-kafka.yaml
- strimzi-kafka-exporter.yaml
- redis-stream-delay.yaml
- player-count.yaml
- aggregations-alerts.yaml
- redis-DLT-messages.yaml
- player-activity.yaml
- player-activity-test.yaml
- pg-top-20-row-count-time-series2.yaml
- pg-slow-queries-devdb.yaml
- pg-tables-difference-stats.yaml
- pg-deleted-rows-expiry-data.yaml
- pg-accounting-transactions.yaml
#- sport-betting-activity-alerting.yaml
#- clickhouse-dashboard.yaml
- go-processes.yaml
- ews-test-deployment.yaml
- pg-aggrigation-executions.yaml
- ssbt-test-metrics.yaml
- victoriametrics
- barman-exporter-dashboard.yaml