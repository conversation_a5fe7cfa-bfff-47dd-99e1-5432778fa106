{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Clickhouse dashboard. Data for dashboard is fetched from Clickhouse system tables. \n\nUseful for single-node installations of Clickhouse.\nTested version of ch - *********.", "editable": true, "gnetId": 13334, "graphTooltip": 0, "id": 838, "links": [], "panels": [{"datasource": "Altinity plugin for ClickHouse UAT", "fieldConfig": {"defaults": {"custom": {}, "decimals": 1, "displayName": "", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 600}]}, "unit": "dtdhms"}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"database": "system", "dateColDataType": "", "dateLoading": false, "dateTimeColDataType": "event_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "extrapolate": true, "format": "table", "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t", "intervalFactor": 1, "query": "SELECT uptime()", "rawQuery": "SELECT uptime()", "refId": "A", "round": "0s", "skip_comments": true, "table": "asynchronous_metric_log", "tableLoading": false}], "timeFrom": null, "timeShift": null, "title": "Uptime", "type": "stat"}, {"datasource": "Altinity plugin for ClickHouse UAT", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 60}, {"color": "red", "value": 300}]}, "unit": "clocks"}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 0}, "id": 9, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"dateTimeType": "DATETIME", "extrapolate": true, "format": "table", "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t", "intervalFactor": 1, "query": "select max(elapsed) longest_query  from system.processes", "rawQuery": "select max(elapsed) longest_query  from system.processes", "refId": "A", "round": "0s", "skip_comments": true}], "timeFrom": null, "timeShift": null, "title": "Longest current query", "type": "stat"}, {"datasource": "Altinity plugin for ClickHouse UAT", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 60}, {"color": "red", "value": 300}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 0, "y": 4}, "id": 10, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": true}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"dateTimeType": "DATETIME", "extrapolate": true, "format": "table", "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t", "intervalFactor": 1, "query": "select uniq(user) \"Users\", uniq(address) \"Hosts\" from system.processes", "rawQuery": "SELECT     concat(database, '.', table) table,     (sum(if(active, bytes, 0))) AS size_on_disk,     (sum(if(NOT active, bytes, 0))) AS inactive_size,     (maxIf(modification_time, active)) AS latest_modification,     formatReadableSize(sum(if(active, data_uncompressed_bytes, 0))) AS uncompressed_size FROM system.parts GROUP BY table ORDER BY sum(if(active, bytes, 0)) desc", "refId": "A", "round": "0s"}], "timeFrom": null, "timeShift": null, "title": "Active clients", "type": "stat"}, {"cacheTimeout": null, "datasource": "ClickHouse UAT", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1000}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 4}, "id": 4, "links": [], "options": {"showHeader": true}, "pluginVersion": "7.4.2", "targets": [{"format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT metric, value FROM system.asynchronous_metrics\r\nWHERE metric IN ('NumberOfDatabases','NumberOfTables');", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Database objects", "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Altinity plugin for ClickHouse UAT", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": false, "avg": true, "current": false, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "count()", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"database": "system", "dateColDataType": "", "dateLoading": false, "dateTimeColDataType": "query_start_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "extrapolate": true, "format": "time_series", "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t", "intervalFactor": 1, "query": "SELECT\n    $timeSeries as t, user,\n    max(query_duration_ms) max_query_duration_ms\nFROM system.query_log\nWHERE $timeFilter\nand type = 'QueryFinish'\ngroup by t, user\nORDER BY user, t", "rawQuery": "select max(elapsed) longest_query  from system.processes", "refId": "A", "round": "0s", "table": "query_log", "tableLoading": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Query duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "clockms", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"decimals": null, "format": "short", "label": "Count", "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"columns": [], "datasource": "Altinity plugin for ClickHouse UAT", "description": "Except  processes from grafana", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 16, "pageSize": 10, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Time", "align": "left", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "", "align": "left", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"database": "system", "dateColDataType": "", "dateLoading": false, "dateTimeType": "DATETIME", "datetimeLoading": false, "extrapolate": true, "format": "table", "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t", "intervalFactor": 1, "query": "SELECT\n    user, query\nFROM $table\nwhere user != 'grafana'\n", "rawQuery": "select max(elapsed) longest_query  from system.processes", "refId": "A", "round": "0s", "table": "processes", "tableLoading": false}], "timeFrom": null, "timeShift": null, "title": "Current processes", "transform": "table", "type": "table-old"}, {"columns": [], "datasource": "Altinity plugin for ClickHouse UAT", "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 6, "pageSize": 10, "pluginVersion": "6.6.0", "showHeader": true, "sort": {"col": null, "desc": false}, "styles": [{"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "mappingType": 1, "pattern": "latest_modification", "thresholds": [], "type": "date", "unit": "dateTimeFromNow"}, {"alias": "", "align": "left", "colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "decimals": 2, "pattern": "size_on_disk", "thresholds": ["1e+12"], "type": "number", "unit": "bits"}, {"alias": "", "align": "auto", "colorMode": "value", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "inactive_size", "thresholds": ["1e+9"], "type": "number", "unit": "decbits"}], "targets": [{"database": "system", "dateColDataType": "", "dateLoading": false, "dateTimeColDataType": "delete_ttl_info_max", "dateTimeType": "DATETIME", "datetimeLoading": false, "extrapolate": true, "format": "table", "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t", "intervalFactor": 1, "query": "SELECT\n    database,\n    (sum(if(active, bytes, 0))) AS size_on_disk,\n    (sum(if(NOT active, bytes, 0))) AS inactive_size,\n    (maxIf(modification_time, active)) AS latest_modification,\n    formatReadableSize(sum(if(active, data_uncompressed_bytes, 0))) AS uncompressed_size\nFROM system.parts\nGROUP BY database\nORDER BY sum(if(active, bytes, 0)) desc", "rawQuery": "SELECT\n    database,\n    (sum(if(active, bytes, 0))) AS size_on_disk,\n    (sum(if(NOT active, bytes, 0))) AS inactive_size,\n    (maxIf(modification_time, active)) AS latest_modification,\n    formatReadableSize(sum(if(active, data_uncompressed_bytes, 0))) AS uncompressed_size\nFROM system.parts\nGROUP BY database\nORDER BY sum(if(active, bytes, 0)) desc", "refId": "A", "round": "0s", "skip_comments": true, "table": "parts", "tableLoading": false}], "timeFrom": null, "timeShift": null, "title": "Database size", "transform": "table", "type": "table-old"}, {"columns": [], "datasource": "Altinity plugin for ClickHouse UAT", "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 7, "pageSize": 10, "pluginVersion": "6.6.0", "showHeader": true, "sort": {"col": null, "desc": false}, "styles": [{"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "mappingType": 1, "pattern": "latest_modification", "thresholds": [], "type": "date", "unit": "dateTimeFromNow"}, {"alias": "", "align": "left", "colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "decimals": 2, "pattern": "size_on_disk", "thresholds": ["1e+12"], "type": "number", "unit": "bits"}, {"alias": "", "align": "auto", "colorMode": "value", "colors": ["#73BF69", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "inactive_size", "thresholds": ["1e+9"], "type": "number", "unit": "decbits"}], "targets": [{"database": "system", "dateColDataType": "", "dateLoading": false, "dateTimeColDataType": "delete_ttl_info_max", "dateTimeType": "DATETIME", "datetimeLoading": false, "extrapolate": true, "format": "table", "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t", "intervalFactor": 1, "query": "SELECT\n    concat(database, '.', table) table,\n    (sum(if(active, bytes, 0))) AS size_on_disk,\n    (sum(if(NOT active, bytes, 0))) AS inactive_size,\n    (maxIf(modification_time, active)) AS latest_modification,\n    formatReadableSize(sum(if(active, data_uncompressed_bytes, 0))) AS uncompressed_size\nFROM system.parts\nGROUP BY table\nORDER BY sum(if(active, bytes, 0)) desc", "rawQuery": "SELECT     concat(database, '.', table) table,     (sum(if(active, bytes, 0))) AS size_on_disk,     (sum(if(NOT active, bytes, 0))) AS inactive_size,     (maxIf(modification_time, active)) AS latest_modification,     formatReadableSize(sum(if(active, data_uncompressed_bytes, 0))) AS uncompressed_size FROM system.parts GROUP BY table ORDER BY sum(if(active, bytes, 0)) desc", "refId": "A", "round": "0s", "table": "parts", "tableLoading": false}], "timeFrom": null, "timeShift": null, "title": "Table size", "transform": "table", "type": "table-old"}, {"columns": [], "datasource": "Altinity plugin for ClickHouse UAT", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 9, "w": 24, "x": 0, "y": 24}, "id": 18, "pageSize": 10, "showHeader": true, "sort": {"col": null, "desc": false}, "styles": [{"alias": "Time", "align": "left", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "", "align": "left", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"database": "system", "dateColDataType": "", "dateLoading": false, "dateTimeColDataType": "event_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "extrapolate": true, "format": "time_series", "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t", "intervalFactor": 1, "query": "select * from (\nSELECT\n    event_time,\n    toString(type) type,\n    exception,\n    query\nFROM $table\nWHERE $timeFilter\nand exception != ''\nunion all \nselect latest_fail_time event_time,\n    'Mutation'   type,\n    latest_fail_reason exception,\n    command query\nfrom system.mutations\nwhere $timeFilter and latest_fail_reason != '') order by event_time desc limit 1000", "rawQuery": "select max(elapsed) longest_query  from system.processes", "refId": "A", "round": "0s", "table": "query_log", "tableLoading": false}], "timeFrom": null, "timeShift": null, "title": "Last query and mutation exceptions", "transform": "timeseries_to_columns", "type": "table-old"}, {"columns": [], "datasource": "Altinity plugin for ClickHouse UAT", "description": "...from system.text_log\nwhere level not in ('Information', 'Debug', 'Trace')...", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 11, "w": 24, "x": 0, "y": 33}, "id": 20, "pageSize": 10, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Time", "align": "", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "t", "type": "date", "unit": "dateTimeAsIso"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "Level", "align": "left", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "level", "preserveFormat": false, "sanitize": true, "thresholds": [""], "type": "string", "unit": "short", "valueMaps": []}, {"alias": "", "align": "left", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "preserveFormat": true, "sanitize": true, "thresholds": [], "type": "string", "unit": "short"}], "targets": [{"database": "system", "dateColDataType": "", "dateLoading": false, "dateTimeColDataType": "event_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "extrapolate": true, "format": "table", "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t", "intervalFactor": 1, "query": "select $timeSeries as t, level, query_id, any(message) message from system.text_log\nwhere level not in ('Information', 'Debug', 'Trace') and $timeFilter\nGROUP BY t, level, query_id\norder by t desc\nlimit 1000", "rawQuery": "select max(elapsed) longest_query  from system.processes", "refId": "A", "round": "0s", "table": "text_log", "tableLoading": false}], "timeFrom": null, "timeShift": null, "title": "Warning and Error from text logs", "transform": "table", "type": "table-old"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Altinity plugin for ClickHouse UAT", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 44}, "hiddenSeries": false, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"database": "system", "dateColDataType": "", "dateLoading": false, "dateTimeColDataType": "event_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "extrapolate": true, "format": "time_series", "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t", "intervalFactor": 1, "query": "SELECT\n    $timeSeries as t,\n    count()\nFROM $table\nWHERE $timeFilter\nand type = 'QueryStart'\nGROUP BY t\nORDER BY t", "rawQuery": "SELECT     (intDiv(toUInt32(event_time), 20) * 20) * 1000 as t,     count() FROM system.query_log WHERE event_time >= toDateTime(1604370507) and type = 'QueryStart' GROUP BY t ORDER BY t", "refId": "A", "round": "0s", "table": "query_log", "tableLoading": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Query starts", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:166", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:167", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "15m", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Clickhouse State UAT", "uid": "r4RxTDhMz2j2h4h2", "version": 2}