---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-vs-victoriametrics-benchmark-dashboard
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: VictoriaMetrics
data:
  prometheus-vs-victoriametrics-benchmark-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": {
              "type": "datasource",
              "uid": "grafana"
            },
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "description": "Following dashboards provide easy comparison between Promethues pod and VictoriaMetrics Virtual machine stats",
      "editable": true,
      "fiscalYearStartMonth": 0,
      "graphTooltip": 0,
      "id": 3229,
      "links": [],
      "panels": [
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "description": "The following dashboard shows used CPUs cores in a rate of 5 minutes for all possible modes except idle",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "prometheus"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "red",
                      "mode": "fixed"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "victoriametrics"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "blue",
                      "mode": "fixed"
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 10,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 2,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{namespace=\"infrastructure\", pod=\"prometheus-kube-prometheus-stack-prometheus-0\", container=\"prometheus\", cluster=\"\"}) by (container)",
              "instant": false,
              "interval": "",
              "legendFormat": "prometheus",
              "refId": "Prometheus"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "sum by (instance) (irate(node_cpu_seconds_total{job=\"victoriametrics-poc-sm\", instance=\"**************:9100\", mode!=\"idle\"}[5m]))",
              "hide": false,
              "interval": "",
              "legendFormat": "victoriametrics",
              "refId": "A"
            }
          ],
          "title": "CPU usage",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "description": "The following panel show used memory for process",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "bytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 24,
            "x": 0,
            "y": 10
          },
          "id": 4,
          "options": {
            "alertThreshold": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "container_memory_working_set_bytes{namespace=\"infrastructure\", pod=\"prometheus-kube-prometheus-stack-prometheus-0\", container=\"prometheus\"}",
              "interval": "",
              "legendFormat": "prometheus",
              "refId": "Prometheus"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "node_memory_MemTotal_bytes{job=\"victoriametrics-poc-sm\", instance=\"**************:9100\"} - node_memory_MemAvailable_bytes{job=\"victoriametrics-poc-sm\", instance=\"**************:9100\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "victoriametrics",
              "refId": "VictoriaMetrics"
            }
          ],
          "title": "RAM usage",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "description": "The following panel shows used disk space by instance",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "bytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 24,
            "x": 0,
            "y": 19
          },
          "id": 6,
          "options": {
            "alertThreshold": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "(\n  sum without(instance, node) (kubelet_volume_stats_capacity_bytes{cluster=\"\", job=\"kubelet\", metrics_path=\"/metrics\", namespace=\"infrastructure\", persistentvolumeclaim=\"prometheus-kube-prometheus-stack-prometheus-db-prometheus-kube-prometheus-stack-prometheus-0\"})\n  -\n  sum without(instance, node) (kubelet_volume_stats_available_bytes{cluster=\"\", job=\"kubelet\", metrics_path=\"/metrics\", namespace=\"infrastructure\", persistentvolumeclaim=\"prometheus-kube-prometheus-stack-prometheus-db-prometheus-kube-prometheus-stack-prometheus-0\"})\n)\n",
              "interval": "",
              "legendFormat": "prometheus",
              "refId": "Prometheus"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "node_filesystem_size_bytes{job=\"victoriametrics-poc-sm\", instance=\"**************:9100\", mountpoint=\"/\"} - node_filesystem_avail_bytes{job=\"victoriametrics-poc-sm\", instance=\"**************:9100\", mountpoint=\"/\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "victoriametrics",
              "refId": "A"
            }
          ],
          "title": "Disk usage",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "description": "Network input bytes",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "bytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 25
          },
          "id": 16,
          "options": {
            "alertThreshold": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "sum(irate(container_network_receive_bytes_total{namespace=~\"infrastructure\", pod=~\"prometheus-kube-prometheus-stack-prometheus-0\"}[5m])) by (pod)",
              "interval": "",
              "legendFormat": "prometheus",
              "refId": "Prometheus"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "irate(node_network_receive_bytes_total{instance=\"**************:9100\",device=~\"(?i)^(ens|eth).+$\"}[5m])",
              "hide": false,
              "interval": "",
              "legendFormat": "victoriametrics",
              "refId": "VictoriaMetrics"
            }
          ],
          "title": "Network input",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "description": "Network output bytes",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "bytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 25
          },
          "id": 17,
          "options": {
            "alertThreshold": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "sum(irate(container_network_transmit_bytes_total{namespace=~\"infrastructure\", pod=~\"prometheus-kube-prometheus-stack-prometheus-0\"}[5m])) by (pod)",
              "interval": "",
              "legendFormat": "prometheus",
              "refId": "Prometheus"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "irate(node_network_transmit_bytes_total{instance=\"**************:9100\",device=~\"(?i)^(ens|eth).+$\"}[5m])",
              "hide": false,
              "interval": "",
              "legendFormat": "victoriametrics",
              "refId": "VictoriaMetrics"
            }
          ],
          "title": "Network output",
          "type": "timeseries"
        }
      ],
      "preload": false,
      "schemaVersion": 40,
      "tags": [
        "prometheus",
        "victoriametrics"
      ],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-2d",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Prometheus vs. VictoriaMetircs resource comparison",
      "uid": "GPGwoidNz",
      "version": 1,
      "weekStart": ""
    }
