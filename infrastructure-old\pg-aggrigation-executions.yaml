apiVersion: v1
kind: ConfigMap
metadata:
  name: aggrigation-executions-devdb
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: infrastructure-dev
data:
  aggrigation-executions-devdb.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 598,
      "links": [],
      "panels": [
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "PostgreSQL-Dev",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "s"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 16,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 2,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "table",
              "group": [],
              "hide": true,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n    date AS time,\n    (date::date + hour * '1 hour'::interval)::timestamp AS date_time,\n    proc::text AS aggregation,\n    extract(epoch FROM (exec_done - exec_start)) AS execution_time\nFROM\n    reporting.stat_aggregate\nWHERE\n    $__timeFilter(date)\nORDER BY\n    1,3,2,4 DESC;\n\n\n",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "value"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "timeColumn": "time",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n    ( date::date + ( hour * '1 hour'::pg_catalog.interval ) )::timestamp AS time,\n    proc::text AS aggregation,\n    extract(epoch FROM (exec_done - exec_start)) AS execution_time\nFROM\n    reporting.stat_aggregate\nwhere\n    $__timeFilter(date)\n    order by 1,2,3 desc;\n",
              "refId": "B",
              "select": [
                [
                  {
                    "params": [
                      "value"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "timeColumn": "time",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": true,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n    a.time AS time,\n    a.aggregation,\n    a.execution_time\nFROM (\n    SELECT\n        (date::date + hour * '1 hour'::interval)::timestamp AS time,\n        proc::text AS aggregation,\n        extract(epoch FROM (exec_done - exec_start)) AS execution_time\n    FROM\n        reporting.stat_aggregate) a\nWHERE\n     $__timeFilter(time)\norder by 1,2,3;\n",
              "refId": "C",
              "select": [
                [
                  {
                    "params": [
                      "value"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "timeColumn": "time",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Aggregation executions",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:34",
              "format": "s",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:35",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": false,
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-24h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Aggregation Exections DevDB",
      "uid": "JSPcVGKVzagge",
      "version": 1
    }