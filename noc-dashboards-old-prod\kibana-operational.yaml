apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-dashboard
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: NOC-dashboards
data:
  kibana-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 992,
      "links": [],
      "panels": [
        {
          "datasource": "Elasticsearch-ews30-crm",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "#EAB839",
                    "value": 130000
                  },
                  {
                    "color": "red",
                    "value": 150000
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 0
          },
          "id": 2,
          "options": {
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "ews30-crm-*",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "NOT \"S:AUDIT\" AND NOT \"ews_dotnet_common.Extensions.AuditLogMiddleware\" AND NOT \"SegmentStoSApi\" AND NOT \"PlayerSToSSegmentController\" AND NOT \"SegmentsByFormulaService\" AND NOT \"ews_crm.Controllers.SToS.PlayerSToSSegmentController\" AND NOT \"EWS.CRM.Application.Players.PlayerSegmentsByFormulaService\" AND NOT \"Polling for\" AND NOT \"ews_crm.Controllers.SToS.PlayerSToSController\" AND NOT \"EWS.CRM.Application.PlayerOperations.PlayerSegmentsByFormulaService\" AND NOT \"ews_dotnet_common.Filters.GlobalExceptionFilter\" AND NOT \"[S2S]\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "CRM > 150K",
          "type": "gauge"
        },
        {
          "datasource": "Elasticsearch-ews-oauth",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "#EAB839",
                    "value": 90000
                  },
                  {
                    "color": "red",
                    "value": 110000
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 0
          },
          "id": 4,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "ews-oauth*",
              "bucketAggs": [
                {
                  "id": "3",
                  "settings": {
                    "filters": [
                      {
                        "label": "",
                        "query": "NOT kubernetes.container_name: \"ews-oauth-ingester\""
                      }
                    ]
                  },
                  "type": "filters"
                },
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "NOT \"Total messages consumed\" AND NOT \"is associated with the provided access token\" AND NOT \"Access token already revoked or could not be revoked\" AND NOT \"Call CAPI\" AND NOT \"s2s\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "OAUTH > 110k",
          "type": "gauge"
        },
        {
          "datasource": "Elasticsearch-ews-30-regul-comp",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 500
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 6,
            "y": 9
          },
          "id": 6,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "(\"Error\" or \"L:Error\" or \"Exception\" or \"Object ref\")",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "NRA vulnerable players search",
          "type": "gauge"
        },
        {
          "datasource": "Elasticsearch-ews-bonuses",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "displayName": "ERRORS",
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "#EAB839",
                    "value": 50
                  },
                  {
                    "color": "semi-dark-red",
                    "value": 51
                  },
                  {
                    "color": "red",
                    "value": 200
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 17
          },
          "id": 24,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "ews-bonuses",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "(\"L:Error\" OR \"Error\" OR \"error\" OR \"unhandled exception\" OR \"FAIL\") AND NOT \"L:Warning\" AND NOT \"L:Information\" AND NOT \"PlayerActivityChoice, error fetching player bonuses\" AND NOT \"PlayerActivityChoice\" AND NOT \"EWS.Bonuses.Services.Bonuses.PlayerBonusesExpirationService\" AND NOT \"/api/sportsapi/public/sport-events/related-sport-events\" AND NOT \"GetRelatedSportEventIds \"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "CAMPAIGN ERRORS",
          "type": "gauge"
        },
        {
          "datasource": "Elasticsearch-ews30-platform-games",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "displayName": "ERRORS",
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "#EAB839",
                    "value": 49
                  },
                  {
                    "color": "red",
                    "value": 50
                  },
                  {
                    "color": "semi-dark-red",
                    "value": 100
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 17
          },
          "id": 26,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "ews30-platform-games",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "(\"L:Error\" OR \"Error\" OR \"error\" OR error OR \"unhandled exception\") AND NOT \"L:Warning\" AND NOT \"L:Information\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "PROMO GAMES",
          "type": "gauge"
        },
        {
          "datasource": "Elasticsearch-ews90-payment",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "displayName": "ERRORS",
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "semi-dark-yellow",
                    "value": 700
                  },
                  {
                    "color": "semi-dark-red",
                    "value": 1500
                  },
                  {
                    "color": "dark-red",
                    "value": 1510
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 6,
            "y": 25
          },
          "id": 16,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"Error\" AND \"HIGH\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "PAYMENTS",
          "type": "gauge"
        },
        {
          "datasource": "Elasticsearch-ews90-payment",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "displayName": "CRITICAL",
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "dark-yellow",
                    "value": 30
                  },
                  {
                    "color": "dark-red",
                    "value": 100
                  },
                  {
                    "color": "dark-red",
                    "value": 3000
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 33
          },
          "id": 10,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "ews90-payment-*",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"APG_GENERATE_TOKEN_ERROR\" OR \"CREATE_CREDIT_CARD_ERROR\" OR \"APG_AUTHORIZE_ERROR\" OR \"APG_TOKEN_GENERATION_ERROR\" OR \"APG_TOKEN_REQUEST_FAILIURE\" OR \"APG_AUTHORIZE_ERROR\" OR \"EASYPAY_API_CALL_ERROR\" OR \"EASYPAY_DEPOSIT_ERROR\" OR \"EASYPAY_WITHDRAW_ERROR\" OR \"EPAY_DEPOSIT_ERROR\" OR \"EASYPAY_WITHDRAW_ERROR\" OR \"FASTPAY_API_CALL_ERROR\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Payment Gateway  Errors",
          "type": "gauge"
        },
        {
          "datasource": "Elasticsearch-ews90-payment",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "displayName": "WARNING",
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "#EAB839",
                    "value": 40
                  },
                  {
                    "color": "red",
                    "value": 100
                  },
                  {
                    "color": "semi-dark-red",
                    "value": 1100
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 11,
            "x": 12,
            "y": 33
          },
          "id": 12,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "ews90-payment-*",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"CREDIT_CARD_NOT_FOUND\" OR \"APG_DEPOSIT_FAILED_TO_GET_ACQUIRER\" OR \"APG_CALLBACK_TRANSFER_STATUS_IS_NOT_CORRECT\" OR \"APG_CALLBACK_STATUS_ERROR\" OR \"JSON_PARSING_ERROR\" OR \"APG_WITHDRAWAL_REQUEST_FAILIURE\" OR \"APG_ACQUIRER_NOT_FOUND\" OR \"EASYPAY_CANCEL_UNKNOWN_RESPONSE\" OR \"EASYPAY_CANCEL_STATE_API_ERROR\" OR \"EASYPAY_CANCEL_STATE_RESPONSE_NOT_OK\" OR \"ERROR_CREATING_TRANSFER\" OR \"EASYPAY_CANCEL_UNKNOWN_RESPONSE\" OR \"EASYPAY_CANCEL_STATE_API_ERROR\" OR \"EASYPAY_CANCEL_STATE_RESPONSE_NOT_OK\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Payment Gateway Errors",
          "type": "gauge"
        },
        {
          "datasource": "Elastic-ews90-gaming-wallet-evolution",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 6,
            "y": 41
          },
          "id": 8,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": true,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "id": "3",
                  "settings": {
                    "filters": [
                      {
                        "label": "ERRORS",
                        "query": "NOT message: \"SMBG\""
                      }
                    ]
                  },
                  "type": "filters"
                },
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"ST:MISSING_BET_COVERAGE\" AND \"T:EVOLUTION_BET_COVERAGE\" and not \"GoldbarRo0000001\" and not \"DoubleBallRou001\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "MISSING_BET_COVERAGE",
          "type": "gauge"
        },
        {
          "datasource": "Elasticsearch-sport-events-offering",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "displayName": "ERRORS",
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "#EAB839",
                    "value": 1000
                  },
                  {
                    "color": "red",
                    "value": 1700
                  },
                  {
                    "color": "semi-dark-red",
                    "value": 2000
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 49
          },
          "id": 14,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "ews-sport-event-offering",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "Error",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Sport Events Offering",
          "type": "gauge"
        },
        {
          "datasource": "Elasticsearch-ews-gaming-feed",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "displayName": "ERRORS",
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "#EAB839",
                    "value": 500
                  },
                  {
                    "color": "dark-red",
                    "value": 1000
                  },
                  {
                    "color": "dark-red",
                    "value": 1010
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 49
          },
          "id": 20,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "ews-gaming -feed",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "(Error) AND NOT \"T:PRAGMATIC_WEB_SOCKET_TRANSFORMER\" AND NOT \"ST:TRANSFORM_PRAGMATIC_STATE\" AND NOT \"NoCacheToArchive\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "GAMING FEED",
          "type": "gauge"
        },
        {
          "datasource": "Elastic-kas-nginx-plus-ingress",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "dark-yellow",
                    "value": 150
                  },
                  {
                    "color": "dark-red",
                    "value": 151
                  },
                  {
                    "color": "red",
                    "value": 300
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 57
          },
          "id": 34,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"/api/gaming/egt\" AND request_time: [4 TO *]",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "REQUEST TIME EGT > 4",
          "type": "gauge"
        },
        {
          "datasource": "Elastic-kas-nginx-plus-ingress",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "dark-yellow",
                    "value": 150
                  },
                  {
                    "color": "semi-dark-red",
                    "value": 151
                  },
                  {
                    "color": "red",
                    "value": 300
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 57
          },
          "id": 32,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"/api/gaming/egti\" AND request_time: [4 TO *]",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "REQUEST TIME AMUSNET > 4",
          "type": "gauge"
        },
        {
          "datasource": "Elastic-kas-nginx-plus-ingress",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 500
                  }
                ]
              },
              "unit": "none"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 65
          },
          "id": 22,
          "interval": null,
          "maxDataPoints": null,
          "options": {
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "nginx-plus-ingress*",
              "bucketAggs": [
                {
                  "id": "1",
                  "settings": {
                    "interval": "auto",
                    "min_doc_count": "0",
                    "trimEdges": "0"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "message:\"/api/\" \nAND request_time:[5 TO *] \nAND NOT http_host:\"inbet-retail-cashier-modern-api.egt-digital.com\" \nAND NOT message:\"gameState\"\nAND NOT message:\"/api/capi\" \nAND NOT message:\"/api/payment\"\n",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "API Request time > 5",
          "type": "gauge"
        },
        {
          "datasource": "Elastic-kas-nginx-plus-ingress",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "yellow",
                    "value": 500
                  },
                  {
                    "color": "red",
                    "value": 510
                  },
                  {
                    "color": "red",
                    "value": 700
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 65
          },
          "id": 30,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"/api/payment\" AND request_time: [5 TO *]",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "PAYMENTS API REQUEST TIME > 5",
          "type": "gauge"
        },
        {
          "datasource": "Elastic-regulation-reporting",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "displayName": "ERRORS",
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "semi-dark-yellow",
                    "value": 120
                  },
                  {
                    "color": "semi-dark-red",
                    "value": 200
                  },
                  {
                    "color": "dark-red",
                    "value": 300
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 73
          },
          "id": 28,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"L:Error\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "REGULATION REPORTING",
          "type": "gauge"
        },
        {
          "datasource": "Elasticsearch-ews30-crm",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "displayName": "ERRORS",
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "semi-dark-yellow",
                    "value": 500
                  },
                  {
                    "color": "dark-red",
                    "value": 1500
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 73
          },
          "id": 18,
          "options": {
            "reduceOptions": {
              "calcs": [
                "sum"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": false,
            "text": {}
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "alias": "ews30-crm*",
              "bucketAggs": [
                {
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "(\"Error\" or \"L:Error\" or \"Exception\") AND NOT \"AffiliateApiClientService\" AND NOT \"Utils\" AND NOT\r\n\"GetPlayerTotalRevenue\" AND NOT \"Not correct timezone header timezone=NaN\" AND NOT \"Error resolving type\"\r\nAND NOT \"Warning\" AND NOT \"Player notification channel is turned off\" AND NOT \"/api/ews-notification-service/s2s/player\" AND NOT \"Player has active restrictions\" AND NOT \"CustomHttpStatusCode\" AND NOT\r\n\"T:HeaderHashNetAppFilter\" AND NOT \"WBRS\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "CRM",
          "type": "gauge"
        }
      ],
      "refresh": "",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-1h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "browser",
      "title": "NOC Kibana",
      "uid": "BhT6m4Dg3",
      "version": 29
    }