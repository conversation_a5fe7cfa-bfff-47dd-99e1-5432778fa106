# PowerShell script to fix BOM issues in JSON files
param(
    [string]$TargetDir = "infrastructure"
)

function Remove-BOMFromFile {
    param([string]$FilePath)
    
    try {
        # Read the file as bytes
        $bytes = [System.IO.File]::ReadAllBytes($FilePath)
        
        # Check if file starts with UTF-8 BOM (EF BB BF)
        if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
            Write-Host "Removing BOM from: $FilePath"
            # Remove the first 3 bytes (BOM) and write back
            $contentWithoutBOM = $bytes[3..($bytes.Length - 1)]
            [System.IO.File]::WriteAllBytes($FilePath, $contentWithoutBOM)
            return $true
        }
        
        # Also check for other potential encoding issues and re-save as UTF-8 without BOM
        $content = Get-Content $FilePath -Raw -Encoding UTF8
        if ($content) {
            # Write back as UTF-8 without BOM
            $utf8NoBom = New-Object System.Text.UTF8Encoding $false
            [System.IO.File]::WriteAllText($FilePath, $content, $utf8NoBom)
            Write-Host "Re-saved as UTF-8 without BOM: $FilePath"
            return $true
        }
    }
    catch {
        Write-Warning "Error processing file $FilePath : $($_.Exception.Message)"
        return $false
    }
    
    return $false
}

function Process-Directory {
    param([string]$Directory)
    
    Write-Host "Processing directory: $Directory"
    
    # Find all dashboard.json files recursively
    $jsonFiles = Get-ChildItem -Path $Directory -Name "dashboard.json" -Recurse
    
    $processedCount = 0
    $fixedCount = 0
    
    foreach ($file in $jsonFiles) {
        $fullPath = Join-Path $Directory $file
        $processedCount++
        
        if (Remove-BOMFromFile $fullPath) {
            $fixedCount++
        }
    }
    
    Write-Host "Processed $processedCount JSON files, fixed $fixedCount files"
}

# Main execution
if (Test-Path $TargetDir) {
    Process-Directory $TargetDir
    Write-Host "BOM fix completed!"
} else {
    Write-Error "Target directory not found: $TargetDir"
}
