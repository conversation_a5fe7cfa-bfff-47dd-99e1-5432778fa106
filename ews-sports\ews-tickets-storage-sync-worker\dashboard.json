{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4747, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "title": "General", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(container_cpu_usage_seconds_total{pod=\"$podname\"}[5m]))", "interval": "", "legendFormat": "cpu usage", "refId": "A"}], "title": "Number of cores used", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 24, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_private_memory_bytes{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_private_memory_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 9}, "id": 14, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "http_request_duration_seconds_sum{instance=\"$instance\", uri!~\".*actuator.*\"}", "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "A"}], "title": "http_request_duration_seconds_sum", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}, "id": 26, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_open_handles{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_open_handles", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 19}, "id": 28, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_num_threads{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_num_threads", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 27}, "id": 30, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_start_time_seconds{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_start_time_seconds", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 27}, "id": 32, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_working_set_bytes{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_working_set_bytes", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "", "value": ""}, "label": "Feed Type", "name": "feed_type", "options": [{"selected": true, "text": "Premium", "value": ""}, {"selected": false, "text": "Standard", "value": "standard-"}], "query": "Premium :   ,Standard : standard-", "type": "custom"}, {"current": {"text": "ews-tickets-storage-sync-worker", "value": "ews-tickets-storage-sync-worker"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(process_private_memory_bytes{}, job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/^${feed_type}ews-tickets-storage-sync-worker.*/", "type": "query"}, {"current": {"text": "ews-dev", "value": "ews-dev"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "172.17.50.230:80", "value": "172.17.50.230:80"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "includeAll": false, "label": "Instance", "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "ews-tickets-storage-sync-worker-789c5d4866-6kjbp", "value": "ews-tickets-storage-sync-worker-789c5d4866-6kjbp"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "hide": 2, "includeAll": false, "label": "Pod", "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "ews-tickets-storage-sync-worker-789c5d4866-6kjbp", "value": "ews-tickets-storage-sync-worker-789c5d4866-6kjbp"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "includeAll": false, "label": "Podname", "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "tickets-storage-sync-worker", "uid": "6eb703473bc44acabed1", "version": 1, "weekStart": ""}