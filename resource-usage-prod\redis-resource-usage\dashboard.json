{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Basic overview of linux host metrics, based on node_exporter", "editable": true, "gnetId": 10180, "graphTooltip": 1, "id": 1371, "iteration": 1659101242841, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 42, "panels": [], "title": "Host Overview", "type": "row"}, {"cacheTimeout": null, "colorBackground": false, "colorPrefix": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 0, "description": "Time since last boot", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 0, "y": 1}, "id": 6, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "(s)", "postfixFontSize": "50%", "prefix": "~", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "node_time_seconds{instance=~\"$host\"} - node_boot_time_seconds{instance=~\"$host\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "Uptime | $host", "type": "singlestat", "valueFontSize": "50%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "description": "Number of processors", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 2, "y": 1}, "id": 2, "interval": "", "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "count(count(node_cpu_seconds_total{instance=~\"$host\"}) by (cpu))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "Processors", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 0, "description": "Amount of memory", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 4, "y": 1}, "id": 4, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "node_memory_MemTotal_bytes{instance=~\"$host\"}", "format": "time_series", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "RAM", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 1, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "percentunit", "gauge": {"maxValue": 1, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 1}, "id": 34, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "1 - avg(irate(node_cpu_seconds_total{mode=\"idle\",instance=~\"$host\"}[5m]))", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "0.6,0.8", "title": "CPU Load", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 1, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "percent", "gauge": {"maxValue": 1, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 1}, "id": 35, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "((node_memory_MemTotal_bytes{instance=~\"$host\"} - node_memory_MemAvailable_bytes{instance=~\"$host\"}) / node_memory_MemTotal_bytes{instance=~\"$host\"}) * 100", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "0.60,0.80", "title": "Memory Use", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 1, "description": "Free diskspace", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "percentunit", "gauge": {"maxValue": 1, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 1}, "id": 8, "interval": "", "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "1 - (sum(node_filesystem_free_bytes{instance=~\"$host\"}) / sum(node_filesystem_size_bytes{instance=~\"$host\"}))", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "0.6,0.8", "title": "Disk Free (Total)", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": null, "description": "Network traffic in the last hour", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "decbytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 15, "y": 1}, "id": 10, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "sum(increase(node_network_receive_bytes_total{instance=~\"$host\"}[24h]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "Net IN (24h)", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": null, "description": "Network traffic in the last hour", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "decbytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 17, "y": 1}, "id": 12, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "sum(increase(node_network_transmit_bytes_total{instance=~\"$host\"}[24h]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "Net OUT (24h)", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"columns": [], "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 4, "w": 5, "x": 19, "y": 1}, "id": 37, "links": [], "pageSize": null, "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Available", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 1, "mappingType": 1, "pattern": "Value", "thresholds": [], "type": "number", "unit": "decbytes"}, {"alias": "Mount", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "mountpoint", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "Type", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "fstype", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "hidden", "unit": "short"}], "targets": [{"expr": "node_filesystem_free_bytes{fstype!~\"(tmpfs|rootfs).*\",instance=~\"$host\"}", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "<PERSON>sk (Free)", "transform": "table", "type": "table-old"}, {"cacheTimeout": null, "colorBackground": false, "colorPrefix": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 0, "description": "Time since last boot", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 0, "y": 5}, "id": 44, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "(s)", "postfixFontSize": "50%", "prefix": "~", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 6, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "node_time_seconds{instance=~\"$host\"} - node_boot_time_seconds{instance=~\"$host\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "Uptime | $host", "type": "singlestat", "valueFontSize": "50%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "description": "Number of processors", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 2, "y": 5}, "id": 46, "interval": "", "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 2, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "count(count(node_cpu_seconds_total{instance=~\"$host\"}) by (cpu))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "Processors", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 0, "description": "Amount of memory", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 4, "y": 5}, "id": 48, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 4, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "node_memory_MemTotal_bytes{instance=~\"$host\"}", "format": "time_series", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "RAM", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 1, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "percentunit", "gauge": {"maxValue": 1, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 5}, "id": 50, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 34, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "1 - avg(irate(node_cpu_seconds_total{mode=\"idle\",instance=~\"$host\"}[5m]))", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "0.6,0.8", "title": "CPU Load", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 1, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "percent", "gauge": {"maxValue": 1, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 5}, "id": 52, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 35, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "((node_memory_MemTotal_bytes{instance=~\"$host\"} - node_memory_MemAvailable_bytes{instance=~\"$host\"}) / node_memory_MemTotal_bytes{instance=~\"$host\"}) * 100", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "0.60,0.80", "title": "Memory Use", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 1, "description": "Free diskspace", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "percentunit", "gauge": {"maxValue": 1, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 5}, "id": 54, "interval": "", "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 8, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "1 - (sum(node_filesystem_free_bytes{instance=~\"$host\"}) / sum(node_filesystem_size_bytes{instance=~\"$host\"}))", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "0.6,0.8", "title": "Disk Free (Total)", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": null, "description": "Network traffic in the last hour", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "decbytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 15, "y": 5}, "id": 56, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 10, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "sum(increase(node_network_receive_bytes_total{instance=~\"$host\"}[24h]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "Net IN (24h)", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": null, "description": "Network traffic in the last hour", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "decbytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 17, "y": 5}, "id": 58, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 12, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "sum(increase(node_network_transmit_bytes_total{instance=~\"$host\"}[24h]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "Net OUT (24h)", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"columns": [], "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 4, "w": 5, "x": 19, "y": 5}, "id": 60, "links": [], "pageSize": null, "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 37, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Available", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 1, "mappingType": 1, "pattern": "Value", "thresholds": [], "type": "number", "unit": "decbytes"}, {"alias": "Mount", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "mountpoint", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "Type", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "fstype", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "hidden", "unit": "short"}], "targets": [{"expr": "node_filesystem_free_bytes{fstype!~\"(tmpfs|rootfs).*\",instance=~\"$host\"}", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "<PERSON>sk (Free)", "transform": "table", "type": "table-old"}, {"cacheTimeout": null, "colorBackground": false, "colorPrefix": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 0, "description": "Time since last boot", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 0, "y": 9}, "id": 45, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "(s)", "postfixFontSize": "50%", "prefix": "~", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 6, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "node_time_seconds{instance=~\"$host\"} - node_boot_time_seconds{instance=~\"$host\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "Uptime | $host", "type": "singlestat", "valueFontSize": "50%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "description": "Number of processors", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 2, "y": 9}, "id": 47, "interval": "", "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 2, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "count(count(node_cpu_seconds_total{instance=~\"$host\"}) by (cpu))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "Processors", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 0, "description": "Amount of memory", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 4, "y": 9}, "id": 49, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 4, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "node_memory_MemTotal_bytes{instance=~\"$host\"}", "format": "time_series", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "RAM", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 1, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "percentunit", "gauge": {"maxValue": 1, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 9}, "id": 51, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 34, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "1 - avg(irate(node_cpu_seconds_total{mode=\"idle\",instance=~\"$host\"}[5m]))", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "0.6,0.8", "title": "CPU Load", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 1, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "percent", "gauge": {"maxValue": 1, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 9}, "id": 53, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 35, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "((node_memory_MemTotal_bytes{instance=~\"$host\"} - node_memory_MemAvailable_bytes{instance=~\"$host\"}) / node_memory_MemTotal_bytes{instance=~\"$host\"}) * 100", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "0.60,0.80", "title": "Memory Use", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 1, "description": "Free diskspace", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "percentunit", "gauge": {"maxValue": 1, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 9}, "id": 55, "interval": "", "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 8, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "1 - (sum(node_filesystem_free_bytes{instance=~\"$host\"}) / sum(node_filesystem_size_bytes{instance=~\"$host\"}))", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "0.6,0.8", "title": "Disk Free (Total)", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": null, "description": "Network traffic in the last hour", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "decbytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 15, "y": 9}, "id": 57, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 10, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "sum(increase(node_network_receive_bytes_total{instance=~\"$host\"}[24h]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "Net IN (24h)", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": null, "description": "Network traffic in the last hour", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "decbytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 4, "w": 2, "x": 17, "y": 9}, "id": 59, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 12, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "sum(increase(node_network_transmit_bytes_total{instance=~\"$host\"}[24h]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "Net OUT (24h)", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"columns": [], "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 4, "w": 5, "x": 19, "y": 9}, "id": 61, "links": [], "pageSize": null, "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 37, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Available", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 1, "mappingType": 1, "pattern": "Value", "thresholds": [], "type": "number", "unit": "decbytes"}, {"alias": "Mount", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "mountpoint", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "Type", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "fstype", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "hidden", "unit": "short"}], "targets": [{"expr": "node_filesystem_free_bytes{fstype!~\"(tmpfs|rootfs).*\",instance=~\"$host\"}", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "<PERSON>sk (Free)", "transform": "table", "type": "table-old"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 13}, "id": 26, "panels": [], "repeat": null, "title": "CPU Details", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 14}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (mode) (irate(node_cpu_seconds_total{instance=~\"$host\"}[5m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{mode}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Load | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 1, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 20}, "hiddenSeries": false, "id": 62, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 14, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (mode) (irate(node_cpu_seconds_total{instance=~\"$host\"}[5m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{mode}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Load | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 1, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 26}, "hiddenSeries": false, "id": 63, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 14, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (mode) (irate(node_cpu_seconds_total{instance=~\"$host\"}[5m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{mode}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Load | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 1, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 32}, "id": 24, "panels": [], "repeat": null, "title": "Memory Details", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 33}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_MemFree_bytes{instance=~\"$host\"} > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Free", "refId": "A"}, {"expr": "node_memory_MemTotal_bytes{instance=~\"$host\"} > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Total", "refId": "B"}, {"expr": "node_memory_MemAvailable_bytes{instance=~\"$host\"} > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "Available", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:399", "decimals": 0, "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:400", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 39}, "hiddenSeries": false, "id": 64, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 16, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_MemFree_bytes{instance=~\"$host\"} > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Free", "refId": "A"}, {"expr": "node_memory_MemTotal_bytes{instance=~\"$host\"} > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Total", "refId": "B"}, {"expr": "node_memory_MemAvailable_bytes{instance=~\"$host\"} > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "Available", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:399", "decimals": 0, "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:400", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 45}, "hiddenSeries": false, "id": 65, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 16, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_MemFree_bytes{instance=~\"$host\"} > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Free", "refId": "A"}, {"expr": "node_memory_MemTotal_bytes{instance=~\"$host\"} > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Total", "refId": "B"}, {"expr": "node_memory_MemAvailable_bytes{instance=~\"$host\"} > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "Available", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:399", "decimals": 0, "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:400", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 51}, "id": 28, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 2, "gridPos": {"h": 6, "w": 14, "x": 0, "y": 4}, "id": 18, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "sort": "min", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "ansible-ctrl1.kred.no:9100", "value": "ansible-ctrl1.kred.no:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_network_receive_bytes_total{instance=~\"$host\",device=~\"(?i)^(ens|eth).+$\"}[5m])  > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "IN ({{device}})", "refId": "A"}, {"expr": "- irate(node_network_transmit_bytes_total{instance=~\"$host\",device=~\"(?i)^(ens|eth).+$\"}[5m]) < 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "OUT ({{device}})", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Network Traffic | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"decimals": null, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 2, "gridPos": {"h": 6, "w": 10, "x": 14, "y": 4}, "id": 43, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "sort": "min", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "ansible-ctrl1.kred.no:9100", "value": "ansible-ctrl1.kred.no:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_network_receive_errs_total{instance=~\"$host\",device=~\"(?i)^(ens|eth).+$\"}[5m]) + irate(node_network_receive_drop_total{instance=~\"$host\",device=~\"(?i)^(ens|eth).+$\"}[5m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Err/Drop IN ({{device}})", "refId": "A"}, {"expr": "- (irate(node_network_transmit_errs_total{instance=~\"$host\",device=~\"(?i)^(ens|eth).+$\"}[5m]) + irate(node_network_transmit_drop_total{instance=~\"$host\",device=~\"(?i)^(ens|eth).+$\"}[5m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Err/Drop OUT ({{device}})", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Network Traffic | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 1, "format": "pps", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"decimals": null, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Network Details", "type": "row"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 52}, "id": 22, "panels": [], "repeat": null, "title": "Disk Details | $host", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 9, "x": 0, "y": 53}, "hiddenSeries": false, "id": 40, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_filesystem_free_bytes{instance=~\"$host\",fstype!~\"(tmpfs|rootfs)\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{mountpoint}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk (Free) | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 9, "y": 53}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_disk_read_bytes_total{instance=~\"$host\"}[5m]) > 0", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Read ({{device}})", "refId": "A"}, {"expr": "- irate(node_disk_written_bytes_total{instance=~\"$host\"}[5m]) < 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Write ({{device}})", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Activity | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 7, "x": 17, "y": 53}, "hiddenSeries": false, "id": 32, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "host", "repeatDirection": "v", "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_disk_read_time_seconds_total{instance=~\"$host\"}[5m]) > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "Read ({{device}})", "refId": "A"}, {"expr": "- irate(node_disk_write_time_seconds_total{instance=~\"$host\"}[5m]) < 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "Write ({{device}})", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk IO | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 9, "x": 0, "y": 60}, "hiddenSeries": false, "id": 66, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 40, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_filesystem_free_bytes{instance=~\"$host\",fstype!~\"(tmpfs|rootfs)\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{mountpoint}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk (Free) | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 9, "y": 60}, "hiddenSeries": false, "id": 68, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 30, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_disk_read_bytes_total{instance=~\"$host\"}[5m]) > 0", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Read ({{device}})", "refId": "A"}, {"expr": "- irate(node_disk_written_bytes_total{instance=~\"$host\"}[5m]) < 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Write ({{device}})", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Activity | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 7, "x": 17, "y": 60}, "hiddenSeries": false, "id": 70, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 32, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_disk_read_time_seconds_total{instance=~\"$host\"}[5m]) > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "Read ({{device}})", "refId": "A"}, {"expr": "- irate(node_disk_write_time_seconds_total{instance=~\"$host\"}[5m]) < 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "Write ({{device}})", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk IO | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 9, "x": 0, "y": 67}, "hiddenSeries": false, "id": 67, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 40, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_filesystem_free_bytes{instance=~\"$host\",fstype!~\"(tmpfs|rootfs)\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{mountpoint}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk (Free) | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 9, "y": 67}, "hiddenSeries": false, "id": 69, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 30, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_disk_read_bytes_total{instance=~\"$host\"}[5m]) > 0", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Read ({{device}})", "refId": "A"}, {"expr": "- irate(node_disk_written_bytes_total{instance=~\"$host\"}[5m]) < 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Write ({{device}})", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Activity | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 7, "x": 17, "y": 67}, "hiddenSeries": false, "id": 71, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "repeatDirection": "v", "repeatIteration": 1659101242841, "repeatPanelId": 32, "scopedVars": {"host": {"selected": true, "text": "**************:9100", "value": "**************:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_disk_read_time_seconds_total{instance=~\"$host\"}[5m]) > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "Read ({{device}})", "refId": "A"}, {"expr": "- irate(node_disk_write_time_seconds_total{instance=~\"$host\"}[5m]) < 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "Write ({{device}})", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk IO | $host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "1m", "schemaVersion": 27, "style": "dark", "tags": ["linux", "node-exporter", "ops"], "templating": {"list": [{"allValue": null, "current": {"selected": true, "tags": [], "text": ["**************:9100", "**************:9100", "**************:9100"], "value": ["**************:9100", "**************:9100", "**************:9100"]}, "datasource": "Prometheus", "definition": "label_values(node_time_seconds{job=\"$job\"},instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Host", "multi": true, "name": "host", "options": [], "query": {"query": "label_values(node_time_seconds{job=\"$job\"},instance)", "refId": "Prometheus-host-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": "redis-acc-all", "value": "redis-acc-all"}, "datasource": "Prometheus", "definition": "label_values(node_boot_time_seconds,job)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [], "query": {"query": "label_values(node_boot_time_seconds,job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/(.*)/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Linux Hosts Metrics - redis", "uid": "ov0oEgd01927ueodjasgo586akjhk", "version": 1}