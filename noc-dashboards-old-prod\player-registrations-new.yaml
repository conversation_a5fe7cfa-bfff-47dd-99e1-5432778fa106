apiVersion: v1
kind: ConfigMap
metadata:
  name: player-registration-new
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: NOC-dashboards
data:
  player-registration-new.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 719,
      "iteration": 1718014646935,
      "links": [],
      "panels": [
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "gridPos": {
            "h": 3,
            "w": 23,
            "x": 0,
            "y": 0
          },
          "id": 5,
          "options": {
            "content": "***<center>${bu}</center>***",
            "mode": "markdown"
          },
          "pluginVersion": "7.4.2",
          "timeFrom": null,
          "timeShift": null,
          "title": "Currently Looking At",
          "type": "text"
        },
        {
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 10,
            "w": 24,
            "x": 0,
            "y": 3
          },
          "id": 3,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "text": {},
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "format": "table",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  sum(count_id)\nFROM monitoring_player_registrations\nWHERE\n  $__timeFilter(create_date) and business_unit IN ($bu)",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Total Player Registrations per Time Range",
          "type": "stat"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 12,
            "w": 24,
            "x": 0,
            "y": 13
          },
          "hiddenSeries": false,
          "id": 2,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\ndate_trunc('hour', public.monitoring_player_registrations.create_date ) AS \"time\",\nbusiness_unit::text AS metric,\nsum(count_id)\nFROM monitoring_player_registrations\nWHERE\n$__timeFilter(create_date) and business_unit IN ($bu)\ngroup by 1,2\nORDER BY 1,2",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Player Registrations per Time Range",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:334",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:335",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "1m",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {
              "selected": true,
              "tags": [],
              "text": [
                "BHBG",
                "FTMX",
                "IBBG",
                "IBBGRT",
                "MBRO",
                "MBRORT",
                "SMBG",
                "VVRO",
                "WBBG",
                "WBRO",
                "WNRS"
              ],
              "value": [
                "BHBG",
                "FTMX",
                "IBBG",
                "IBBGRT",
                "MBRO",
                "MBRORT",
                "SMBG",
                "VVRO",
                "WBBG",
                "WBRO",
                "WNRS"
              ]
            },
            "datasource": "audit-logs-monitoring-db",
            "definition": "SELECT DISTINCT business_unit FROM monitoring_player_registrations",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": null,
            "multi": true,
            "name": "bu",
            "options": [
              {
                "selected": false,
                "text": "All",
                "value": "$__all"
              },
              {
                "selected": true,
                "text": "BHBG",
                "value": "BHBG"
              },
              {
                "selected": true,
                "text": "FTMX",
                "value": "FTMX"
              },
              {
                "selected": true,
                "text": "IBBG",
                "value": "IBBG"
              },
              {
                "selected": true,
                "text": "IBBGRT",
                "value": "IBBGRT"
              },
              {
                "selected": true,
                "text": "MBRO",
                "value": "MBRO"
              },
              {
                "selected": true,
                "text": "MBRORT",
                "value": "MBRORT"
              },
              {
                "selected": true,
                "text": "SMBG",
                "value": "SMBG"
              },
              {
                "selected": true,
                "text": "VVRO",
                "value": "VVRO"
              },
              {
                "selected": true,
                "text": "WBBG",
                "value": "WBBG"
              },
              {
                "selected": true,
                "text": "WBRO",
                "value": "WBRO"
              },
              {
                "selected": true,
                "text": "WNRS",
                "value": "WNRS"
              }
            ],
            "query": "SELECT DISTINCT business_unit FROM monitoring_player_registrations",
            "refresh": 0,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-24h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "NOC - Player Registrations",
      "uid": "y6Q0Xgsaegtnew",
      "version": 2
    }