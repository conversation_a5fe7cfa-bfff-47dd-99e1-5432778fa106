{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "iteration": 1711359374916, "links": [], "panels": [{"datasource": "PostgreSQL-Dev", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 0}, "id": 10, "options": {"showHeader": true}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  last_reset_temporary_limit AS \"last_reset (UTC)\",\n  betting_shop_default_limit AS \"default_limit\",\n  betting_shop_temporary_limit AS \"temporary_limit\",\n  id\nFROM retail.points_of_sales\nWHERE\n  business_unit = 'IBBGRT'\nORDER BY id", "refId": "A", "select": [[{"params": ["last_reset_temporary_limit"], "type": "column"}], [{"params": ["betting_shop_default_limit"], "type": "column"}], [{"params": ["betting_shop_temporary_limit"], "type": "column"}], [{"params": ["id"], "type": "column"}]], "table": "retail.points_of_sales", "timeColumn": "last_reset_temporary_limit", "timeColumnType": "timestamp", "where": [{"datatype": "<PERSON><PERSON><PERSON>", "name": "", "params": ["business_unit", "=", "'IBBGRT'"], "type": "expression"}]}], "timeFrom": null, "timeShift": null, "title": "IBBGRT Point of sales - Limits DEV", "type": "table"}, {"datasource": "PostgreSQL-Uat", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 12, "options": {"showHeader": true, "sortBy": [{"desc": false, "displayName": "id"}]}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  last_reset_temporary_limit AS \"last_reset (UTC)\",\n  betting_shop_default_limit AS \"default_limit\",\n  betting_shop_temporary_limit AS \"temporary_limit\",\n  id\nFROM retail.points_of_sales\nWHERE\n  business_unit = 'IBBGRT'\nORDER BY id", "refId": "A", "select": [[{"params": ["last_reset_temporary_limit"], "type": "column"}], [{"params": ["betting_shop_default_limit"], "type": "column"}], [{"params": ["betting_shop_temporary_limit"], "type": "column"}], [{"params": ["id"], "type": "column"}]], "table": "retail.points_of_sales", "timeColumn": "last_reset_temporary_limit", "timeColumnType": "timestamp", "where": [{"datatype": "<PERSON><PERSON><PERSON>", "name": "", "params": ["business_unit", "=", "'IBBGRT'"], "type": "expression"}]}], "timeFrom": null, "timeShift": null, "title": "IBBGRT Point of sales - Limits UAT", "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 7}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total{pod=\"$podname\"}[5m]))", "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Number of cores", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:37", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:38", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "PostgreSQL-Int", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "last_reset (UTC)"}, "properties": [{"id": "custom.width", "value": 225}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "last_reset (Local Time)"}, "properties": [{"id": "custom.width", "value": 235}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 11, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  last_reset_temporary_limit AS \"last_reset (UTC)\",\n  betting_shop_default_limit AS \"default_limit\",\n  betting_shop_temporary_limit AS \"temporary_limit\",\n  id\nFROM retail.points_of_sales\nWHERE\n  business_unit = 'IBBGRT'\nORDER BY id", "refId": "A", "select": [[{"params": ["last_reset_temporary_limit"], "type": "column"}], [{"params": ["betting_shop_default_limit"], "type": "column"}], [{"params": ["betting_shop_temporary_limit"], "type": "column"}], [{"params": ["id"], "type": "column"}]], "table": "retail.points_of_sales", "timeColumn": "last_reset_temporary_limit", "timeColumnType": "timestamp", "where": [{"datatype": "<PERSON><PERSON><PERSON>", "name": "", "params": ["business_unit", "=", "'IBBGRT'"], "type": "expression"}]}], "timeFrom": null, "timeShift": null, "title": "IBBGRT Point of sales - Limits INT", "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 16}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_private_memory_bytes{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Process Private Memory Bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:159", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:160", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "PostgreSQL-Uat", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "color-text", "filterable": true}, "mappings": [{"from": "", "id": 1, "text": "", "to": "", "type": 1}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 12, "x": 12, "y": 16}, "id": 15, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "WITH max_reset_temp_limit AS (\r\n  SELECT TO_CHAR(last_reset_temporary_limit, 'YYYY-MM-DD HH24:MI:SS') AS max_last_reset_temporary_limit\r\n  FROM retail.points_of_sales\r\n  WHERE business_unit = 'IBBGRT'\r\n  LIMIT 1\r\n),\r\nrecords_with_difference AS (\r\n  SELECT\r\n    id,\r\n    TO_CHAR(last_reset_temporary_limit, 'YYYY-MM-DD HH24:MI:SS') AS last_reset_temporary_limit\r\n  FROM retail.points_of_sales\r\n  CROSS JOIN max_reset_temp_limit\r\n  WHERE \r\n    business_unit = 'IBBGRT'\r\n    AND TO_CHAR(last_reset_temporary_limit, 'YYYY-MM-DD HH24:MI:SS') <> max_last_reset_temporary_limit\r\n)\r\nSELECT \r\n  id,\r\n  last_reset_temporary_limit\r\nFROM records_with_difference\r\nUNION ALL\r\nSELECT \r\n  NULL AS id,  -- Placeholder for max_reset_temp_limit when there are no differences\r\n  max_last_reset_temporary_limit AS last_reset_temporary_limit\r\nFROM max_reset_temp_limit\r\nWHERE NOT EXISTS (\r\n  SELECT 1 FROM records_with_difference\r\n)\r\nORDER BY id;\r\n", "refId": "A", "select": [[{"params": ["last_reset_temporary_limit"], "type": "column"}], [{"params": ["betting_shop_default_limit"], "type": "column"}], [{"params": ["betting_shop_temporary_limit"], "type": "column"}], [{"params": ["id"], "type": "column"}]], "table": "retail.points_of_sales", "timeColumn": "last_reset_temporary_limit", "timeColumnType": "timestamp", "where": [{"datatype": "<PERSON><PERSON><PERSON>", "name": "", "params": ["business_unit", "=", "'IBBGRT'"], "type": "expression"}]}], "timeFrom": null, "timeShift": null, "title": "IBBGRT Point of sales - Limits UAT", "type": "table"}, {"datasource": "PostgreSQL-Dev", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "color-text", "filterable": true}, "mappings": [{"from": "", "id": 1, "text": "", "to": "", "type": 1}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 12, "x": 12, "y": 19}, "id": 13, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "WITH max_reset_temp_limit AS (\r\n  SELECT TO_CHAR(last_reset_temporary_limit, 'YYYY-MM-DD HH24:MI:SS') AS max_last_reset_temporary_limit\r\n  FROM retail.points_of_sales\r\n  WHERE business_unit = 'IBBGRT'\r\n  LIMIT 1\r\n),\r\nrecords_with_difference AS (\r\n  SELECT\r\n    id,\r\n    TO_CHAR(last_reset_temporary_limit, 'YYYY-MM-DD HH24:MI:SS') AS last_reset_temporary_limit\r\n  FROM retail.points_of_sales\r\n  CROSS JOIN max_reset_temp_limit\r\n  WHERE \r\n    business_unit = 'IBBGRT'\r\n    AND TO_CHAR(last_reset_temporary_limit, 'YYYY-MM-DD HH24:MI:SS') <> max_last_reset_temporary_limit\r\n)\r\nSELECT \r\n  id,\r\n  last_reset_temporary_limit\r\nFROM records_with_difference\r\nUNION ALL\r\nSELECT \r\n  NULL AS id,  -- Placeholder for max_reset_temp_limit when there are no differences\r\n  max_last_reset_temporary_limit AS last_reset_temporary_limit\r\nFROM max_reset_temp_limit\r\nWHERE NOT EXISTS (\r\n  SELECT 1 FROM records_with_difference\r\n)\r\nORDER BY id;\r\n", "refId": "A", "select": [[{"params": ["last_reset_temporary_limit"], "type": "column"}], [{"params": ["betting_shop_default_limit"], "type": "column"}], [{"params": ["betting_shop_temporary_limit"], "type": "column"}], [{"params": ["id"], "type": "column"}]], "table": "retail.points_of_sales", "timeColumn": "last_reset_temporary_limit", "timeColumnType": "timestamp", "where": [{"datatype": "<PERSON><PERSON><PERSON>", "name": "", "params": ["business_unit", "=", "'IBBGRT'"], "type": "expression"}]}], "timeFrom": null, "timeShift": null, "title": "IBBGRT Point of sales - Limits DEV", "type": "table"}, {"datasource": "PostgreSQL-Int", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "color-text", "filterable": true}, "mappings": [{"from": "", "id": 1, "text": "", "to": "", "type": 1}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 22}, "id": 14, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "WITH max_reset_temp_limit AS (\r\n  SELECT TO_CHAR(last_reset_temporary_limit, 'YYYY-MM-DD HH24:MI:SS') AS max_last_reset_temporary_limit\r\n  FROM retail.points_of_sales\r\n  WHERE business_unit = 'IBBGRT'\r\n  LIMIT 1\r\n),\r\nrecords_with_difference AS (\r\n  SELECT\r\n    id,\r\n    TO_CHAR(last_reset_temporary_limit, 'YYYY-MM-DD HH24:MI:SS') AS last_reset_temporary_limit\r\n  FROM retail.points_of_sales\r\n  CROSS JOIN max_reset_temp_limit\r\n  WHERE \r\n    business_unit = 'IBBGRT'\r\n    AND TO_CHAR(last_reset_temporary_limit, 'YYYY-MM-DD HH24:MI:SS') <> max_last_reset_temporary_limit\r\n)\r\nSELECT \r\n  id,\r\n  last_reset_temporary_limit\r\nFROM records_with_difference\r\nUNION ALL\r\nSELECT \r\n  NULL AS id,  -- Placeholder for max_reset_temp_limit when there are no differences\r\n  max_last_reset_temporary_limit AS last_reset_temporary_limit\r\nFROM max_reset_temp_limit\r\nWHERE NOT EXISTS (\r\n  SELECT 1 FROM records_with_difference\r\n)\r\nORDER BY id;\r\n", "refId": "A", "select": [[{"params": ["last_reset_temporary_limit"], "type": "column"}], [{"params": ["betting_shop_default_limit"], "type": "column"}], [{"params": ["betting_shop_temporary_limit"], "type": "column"}], [{"params": ["id"], "type": "column"}]], "table": "retail.points_of_sales", "timeColumn": "last_reset_temporary_limit", "timeColumnType": "timestamp", "where": [{"datatype": "<PERSON><PERSON><PERSON>", "name": "", "params": ["business_unit", "=", "'IBBGRT'"], "type": "expression"}]}], "timeFrom": null, "timeShift": null, "title": "IBBGRT Point of sales - Limits INT", "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 26}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(http_request_duration_seconds_sum{instance=\"$instance\"}[5m])/rate(http_request_duration_seconds_count{instance=\"$instance\"}[5m])", "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "HTTP request duration seconds", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:217", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:218", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 35}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(http_request_duration_seconds_count{instance=\"$instance\"}[1m])", "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Http request duration rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:277", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:278", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "5s", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "ews-retail-cron", "value": "ews-retail-cron"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{}, job)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-retail.*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-dev", "value": "ews-dev"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "*************:80", "value": "*************:80"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-retail-cron-654f5f96c4-4r85w", "value": "ews-retail-cron-654f5f96c4-4r85w"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Pod", "multi": false, "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-retail-cron-654f5f96c4-4r85w", "value": "ews-retail-cron-654f5f96c4-4r85w"}, "datasource": null, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Podname", "multi": false, "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Retail services", "uid": "asdIY123jansH123Y", "version": 3}