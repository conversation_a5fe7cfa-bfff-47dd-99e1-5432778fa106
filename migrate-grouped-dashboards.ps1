# PowerShell script to migrate grouped dashboards from infrastructure-old to infrastructure
param(
    [string]$SourceDir = "infrastructure-old",
    [string]$TargetDir = "infrastructure"
)

# List of grouped dashboard folders
$groupedFolders = @(
    "clickhouse",
    "postgres-experimental", 
    "scylla",
    "vault",
    "victoriametrics"
)

function Extract-JsonFromYaml {
    param([string]$FilePath)
    
    $content = Get-Content $FilePath -Raw
    # Find the JSON content after "data:" section
    $jsonStart = $content.IndexOf('|-')
    if ($jsonStart -eq -1) {
        $jsonStart = $content.IndexOf('|')
    }
    
    if ($jsonStart -ne -1) {
        $jsonContent = $content.Substring($jsonStart + 2).Trim()
        # Remove leading spaces from each line (YAML indentation)
        $lines = $jsonContent -split "`n"
        $cleanedLines = @()
        foreach ($line in $lines) {
            if ($line.Length -gt 4) {
                $cleanedLines += $line.Substring(4)
            } else {
                $cleanedLines += $line
            }
        }
        return $cleanedLines -join "`n"
    }
    return $null
}

function Get-DashboardName {
    param([string]$FileName)
    
    return $FileName -replace '\.yaml$', ''
}

function Create-DashboardStructure {
    param(
        [string]$DashboardName,
        [string]$JsonContent,
        [string]$TargetDir
    )
    
    $dashboardDir = Join-Path $TargetDir $DashboardName
    New-Item -ItemType Directory -Path $dashboardDir -Force | Out-Null
    
    # Create dashboard.json
    $jsonPath = Join-Path $dashboardDir "dashboard.json"
    $JsonContent | Out-File -FilePath $jsonPath -Encoding UTF8
    
    # Create dashboard.yaml
    $yamlContent = @"
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaDashboard
metadata:
  name: $DashboardName
spec:
  allowCrossNamespaceImport: true
  resyncPeriod: 30s
  instanceSelector:
    matchLabels:
      dashboards: "grafana"
  configMapRef:
    name: $DashboardName
    key: dashboard.json
"@
    
    $yamlPath = Join-Path $dashboardDir "dashboard.yaml"
    $yamlContent | Out-File -FilePath $yamlPath -Encoding UTF8
    
    # Create kustomization.yaml
    $kustomizationContent = @"
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - dashboard.yaml

generatorOptions:
  disableNameSuffixHash: true
  labels:
    grafana_dashboard: "true"
    
configMapGenerator:
  - name: $DashboardName
    files:
      - dashboard.json
"@
    
    $kustomizationPath = Join-Path $dashboardDir "kustomization.yaml"
    $kustomizationContent | Out-File -FilePath $kustomizationPath -Encoding UTF8
    
    Write-Host "Created dashboard structure for: $DashboardName"
}

function Process-GroupedFolder {
    param(
        [string]$GroupName,
        [string]$SourceDir,
        [string]$TargetDir
    )
    
    $sourceGroupDir = Join-Path $SourceDir $GroupName
    $targetGroupDir = Join-Path $TargetDir $GroupName
    
    if (-not (Test-Path $sourceGroupDir)) {
        Write-Warning "Source group directory not found: $sourceGroupDir"
        return
    }
    
    # Create target group directory
    New-Item -ItemType Directory -Path $targetGroupDir -Force | Out-Null
    Write-Host "Processing group: $GroupName"
    
    # Get all YAML files in the source group directory (excluding kustomization.yaml)
    $yamlFiles = Get-ChildItem -Path $sourceGroupDir -Filter "*.yaml" | Where-Object { $_.Name -ne "kustomization.yaml" }
    
    $resources = @()
    
    foreach ($file in $yamlFiles) {
        $dashboardName = Get-DashboardName $file.Name
        $fullDashboardName = "$GroupName-$dashboardName"
        
        Write-Host "  Processing: $($file.Name) -> $fullDashboardName"
        
        $jsonContent = Extract-JsonFromYaml $file.FullName
        if ($jsonContent) {
            Create-DashboardStructure $fullDashboardName $jsonContent $targetGroupDir
            $resources += "  - $fullDashboardName"
        } else {
            Write-Warning "Could not extract JSON from $($file.FullName)"
        }
    }
    
    # Create group-level kustomization.yaml
    $groupKustomizationContent = @"
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
$($resources -join "`n")
"@
    
    $groupKustomizationPath = Join-Path $targetGroupDir "kustomization.yaml"
    $groupKustomizationContent | Out-File -FilePath $groupKustomizationPath -Encoding UTF8
    
    Write-Host "Created group kustomization for: $GroupName"
}

# Main migration logic for grouped folders
foreach ($group in $groupedFolders) {
    $targetPath = Join-Path $TargetDir $group
    
    # Skip if already exists and has content
    if ((Test-Path $targetPath) -and (Get-ChildItem $targetPath -Recurse | Measure-Object).Count -gt 1) {
        Write-Host "Skipping $group - already exists with content"
        continue
    }
    
    Process-GroupedFolder $group $SourceDir $TargetDir
}

Write-Host "Grouped dashboard migration completed!"
