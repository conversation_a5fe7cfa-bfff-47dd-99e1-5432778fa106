apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-dlt-messages
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: infrastructure-dev
data:
  redis-DLT-Messages.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 323,
      "links": [],
      "panels": [
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 2,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "consumerGroupMetrics{job=\"acc-redis-metrics\", streamName=~\".+\", key=\"dltlen\", namespace=\"ews-dev\"}",
              "format": "time_series",
              "interval": "",
              "legendFormat": "stream, consumerGroup: {{streamName}}, {{consumerGroup}}",
              "refId": "A"
            },
            {
              "expr": "consumerGroupMetrics{job=\"bet-redis-metrics\", streamName=~\".+\", key=\"dltlen\", namespace=\"ews-dev\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "stream, consumerGroup: {{streamName}}, {{consumerGroup}}",
              "refId": "B"
            },
            {
              "expr": "consumerGroupMetrics{job=\"spo-redis-metrics\", streamName=~\".+\", key=\"dltlen\", namespace=\"ews-dev\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "stream, consumerGroup: {{streamName}}, {{consumerGroup}}",
              "refId": "C"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "DLT Messages",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:57",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:58",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "DLT Messages",
      "uid": "7dBVRQP7z",
      "version": 1
    }