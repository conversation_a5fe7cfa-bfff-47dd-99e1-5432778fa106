# PowerShell script to migrate dashboards from infrastructure-old to infrastructure
param(
    [string]$SourceDir = "vault-old-prod",
    [string]$TargetDir = "vault-prod"
)

# Automatically discover all YAML files in the source directory
Write-Host "Discovering YAML files in: $SourceDir"
if (-not (Test-Path $SourceDir)) {
    Write-Error "Source directory not found: $SourceDir"
    exit 1
}

$dashboardFiles = Get-ChildItem -Path $SourceDir -Filter "*.yaml" | Select-Object -ExpandProperty Name
Write-Host "Found $($dashboardFiles.Count) YAML files:"
foreach ($file in $dashboardFiles) {
    Write-Host "  - $file"
}

function Extract-JsonFromYaml {
    param([string]$FilePath)
    
    $content = Get-Content $FilePath -Raw
    # Find the JSON content after "data:" section
    $jsonStart = $content.IndexOf('|-')
    if ($jsonStart -eq -1) {
        $jsonStart = $content.IndexOf('|')
    }
    
    if ($jsonStart -ne -1) {
        $jsonContent = $content.Substring($jsonStart + 2).Trim()
        # Remove leading spaces from each line (YAML indentation)
        $lines = $jsonContent -split "`n"
        $cleanedLines = @()
        foreach ($line in $lines) {
            if ($line.Length -gt 4) {
                $cleanedLines += $line.Substring(4)
            } else {
                $cleanedLines += $line
            }
        }
        return $cleanedLines -join "`n"
    }
    return $null
}

function Get-DashboardName {
    param([string]$FileName)
    
    return $FileName -replace '\.yaml$', ''
}

function Create-DashboardStructure {
    param(
        [string]$DashboardName,
        [string]$JsonContent,
        [string]$TargetDir
    )
    
    $dashboardDir = Join-Path $TargetDir $DashboardName
    New-Item -ItemType Directory -Path $dashboardDir -Force | Out-Null
    
    # Create dashboard.json
    $jsonPath = Join-Path $dashboardDir "dashboard.json"
    $JsonContent | Out-File -FilePath $jsonPath -Encoding UTF8
    
    # Create dashboard.yaml
    $yamlContent = @"
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaDashboard
metadata:
  name: $DashboardName
spec:
  allowCrossNamespaceImport: true
  resyncPeriod: 30s
  instanceSelector:
    matchLabels:
      dashboards: "grafana"
  configMapRef:
    name: $DashboardName
    key: dashboard.json
"@
    
    $yamlPath = Join-Path $dashboardDir "dashboard.yaml"
    $yamlContent | Out-File -FilePath $yamlPath -Encoding UTF8
    
    # Create kustomization.yaml
    $kustomizationContent = @"
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - dashboard.yaml

generatorOptions:
  disableNameSuffixHash: true
  labels:
    grafana_dashboard: "true"
    
configMapGenerator:
  - name: $DashboardName
    files:
      - dashboard.json
"@
    
    $kustomizationPath = Join-Path $dashboardDir "kustomization.yaml"
    $kustomizationContent | Out-File -FilePath $kustomizationPath -Encoding UTF8
    
    Write-Host "Created dashboard structure for: $DashboardName"
}

# Main migration logic
foreach ($file in $dashboardFiles) {
    $sourcePath = Join-Path $SourceDir $file
    
    if (Test-Path $sourcePath) {
        $dashboardName = Get-DashboardName $file
        $targetPath = Join-Path $TargetDir $dashboardName
        
        # Skip if already exists
        if (Test-Path $targetPath) {
            Write-Host "Skipping $dashboardName - already exists"
            continue
        }
        
        Write-Host "Processing: $file -> $dashboardName"
        
        $jsonContent = Extract-JsonFromYaml $sourcePath
        if ($jsonContent) {
            Create-DashboardStructure $dashboardName $jsonContent $TargetDir
        } else {
            Write-Warning "Could not extract JSON from $file"
        }
    } else {
        Write-Warning "Source file not found: $sourcePath"
    }
}

Write-Host "Migration completed!"
