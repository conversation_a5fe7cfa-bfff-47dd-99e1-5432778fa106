
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: scylla-detailed-dashboard
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: ScyllaDB
data:
  scylla-detailed-dashboard.json: |-
    {"annotations":{"class":"default_annotations","list":[{"builtIn":1,"datasource":{"type":"datasource","uid":"grafana"},"enable":false,"hide":true,"iconColor":"rgba(0, 211, 255, 1)","name":"Annotations & Alerts","type":"dashboard"},{"class":"annotation_restart","datasource":"Prometheus","enable":true,"expr":"resets(scylla_gossip_heart_beat{cluster=\"$cluster\"}[$__rate_interval])>0","hide":false,"iconColor":"rgba(255, 96, 96, 1)","limit":100,"name":"node_restart","showIn":0,"tagKeys":"instance,dc,cluster","tags":[],"titleFormat":"restart","type":"tags"},{"class":"annotation_stall","datasource":"Prometheus","enable":false,"expr":"changes(scylla_stall_detector_reported{cluster=\"$cluster\"}[$__rate_interval])>0","hide":false,"iconColor":"rgba(255, 96, 96, 1)","limit":100,"name":"stall detector","showIn":0,"tagKeys":"dc,instance,shard","tags":[],"titleFormat":"Stall found","type":"tags"},{"class":"annotation_schema_changed","datasource":"Prometheus","enable":false,"expr":"changes(scylla_database_schema_changed{cluster=\"$cluster\"}[$__rate_interval])>0","hide":false,"iconColor":"rgba(255, 96, 96, 1)","limit":100,"name":"Schema Changed","showIn":0,"tagKeys":"instance,dc,cluster","tags":[],"titleFormat":"schema changed","type":"tags"},{"class":"annotation_manager_task","datasource":"Prometheus","enable":true,"expr":"scylla_manager_task_active_count{type=~\"repair|backup\",cluster=\"$cluster\"}>0","hide":false,"iconColor":"#73BF69","limit":100,"name":"Task","showIn":0,"tagKeys":"type","tags":[],"titleFormat":"Running","type":"tags"},{"class":"annotation_hints_writes","datasource":"Prometheus","enable":false,"expr":"changes(scylla_hints_manager_written{cluster=\"$cluster\"}[$__rate_interval])>0","hide":false,"iconColor":"rgb(255, 176, 0, 128)","limit":100,"name":"Hints Write","showIn":0,"tagKeys":"instance,dc,cluster","tags":[],"titleFormat":"Hints write","type":"tags"},{"class":"annotation_hints_sent","datasource":"Prometheus","enable":false,"expr":"changes(scylla_hints_manager_sent{cluster=\"$cluster\"}[$__rate_interval])>0","hide":false,"iconColor":"rgb(50, 176, 0, 128)","limit":100,"name":"Hints Sent","showIn":0,"tagKeys":"instance,dc,cluster","tags":[],"titleFormat":"Hints Sent","type":"tags"},{"class":"mv_building","datasource":"Prometheus","enable":true,"expr":"sum(scylla_view_builder_builds_in_progress{cluster=\"$cluster\"})>0","hide":false,"iconColor":"rgb(50, 176, 0, 128)","limit":100,"name":"MV","showIn":0,"tagKeys":"instance,dc,cluster","tags":[],"titleFormat":"Materialized View built","type":"tags"},{"class":"ops_annotation","datasource":"Prometheus","enable":true,"expr":"10*min(scylla_node_ops_finished_percentage{cluster=\"$cluster\"}) by (ops, dc,instance) < 10","hide":false,"iconColor":"rgb(50, 176, 0, 128)","limit":100,"name":"ops","showIn":0,"tagKeys":"ops,dc,instance","tags":[],"titleFormat":"Operation","type":"tags"},{"class":"stream_annotation","dashversion":[">5.2",">2023.1"],"datasource":"Prometheus","enable":true,"expr":"10*min(scylla_streaming_finished_percentage{cluster=\"$cluster\"}) by (ops, dc,instance) < 10","hide":false,"iconColor":"rgb(50, 176, 0, 128)","limit":100,"name":"streaming","showIn":0,"tagKeys":"ops,dc,instance","tags":[],"titleFormat":"Streaming","type":"tags"}]},"editable":true,"gnetId":null,"graphTooltip":1,"iteration":1728456072941,"links":[{"asDropdown":true,"icon":"external link","includeVars":true,"keepTime":true,"tags":[],"type":"dashboards"}],"panels":[{"class":"text_panel","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":3,"w":24,"x":0,"y":0},"id":1,"isNew":true,"links":[],"mode":"html","options":{"content":"<div>\n<span style=\"font-size:40px\">  [[cluster]]</span><span style=\"padding-top: 25px;float:right\"></span><hr style=\"border-top: 3px solid #5780c1;\"></div>","mode":"html"},"pluginVersion":"7.4.2","span":12,"title":"","transparent":true,"type":"text"},{"class":"percent_panel","datasource":"Prometheus","description":"The percentage of the time during which Scylla utilized the CPU. Note that because Scylla does busy polling for some time before going idle, CPU utilization as seen by the operating system may be much higher. Your system is not yet CPU-bottlenecked until this metric is high.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"percent"},"overrides":[]},"gridPos":{"h":6,"w":4,"x":0,"y":3},"id":2,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":5,"targets":[{"expr":"topk($topk, avg(scylla_reactor_utilization{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]])) or on ([[by]]) bottomk($bottomk, avg(scylla_reactor_utilization{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} ) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":4}],"title":"Load","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"Amount of requests served as the coordinator. Imbalances here represent dispersion at the connection level, not your data model.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":5,"x":4,"y":3},"id":3,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":5,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_transport_requests_served{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_transport_requests_served{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"Requests Served per [[by]] - Coordinator","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"Counts the total number of successful user reads on this shard.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":5,"x":9,"y":3},"id":4,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":5,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_database_total_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_database_total_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Reads per [[by]] - Replica","type":"timeseries"},{"class":"wps_panel","datasource":"Prometheus","description":"Counts the total number of successful write operations performed by this shard.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":5,"x":14,"y":3},"id":5,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":5,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_database_total_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_database_total_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"Writes per [[by]] - Replica","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":5,"x":19,"y":3},"id":6,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":5,"targets":[{"expr":"topk([[topk]], sum(scylla_tablets_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(scylla_tablets_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by([[by]]))","format":"time_series","instant":false,"legendFormat":"{{dc}} {{instance}} {{shard}}","range":false,"refId":"A"}],"title":"Tablets over time per [[by]]","type":"timeseries"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":9},"id":7,"panels":[],"repeat":"","title":"Tablets information","type":"row"},{"cards":{"cardPadding":null,"cardRound":null},"class":"heatmap_panel","color":{"cardColor":"#b4ff00","colorScale":"sqrt","colorScheme":"interpolateOranges","exponent":0.5,"mode":"spectrum"},"dataFormat":"timeseries","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{"hideFrom":{},"scaleDistribution":{}}},"overrides":[]},"gridPos":{"h":13,"w":24,"x":0,"y":10},"heatmap":{},"hideZeroBuckets":false,"highlightCards":true,"id":8,"isNew":true,"legend":{"show":false},"links":[],"options":{"barRadius":0,"barWidth":0.89,"calculate":false,"cellGap":1,"color":{"exponent":0.5,"fill":"dark-orange","min":0,"mode":"scheme","reverse":false,"scale":"exponential","scheme":"Oranges","steps":64},"exemplars":{"color":"rgba(255,0,255,0.7)"},"filterValues":{"le":1e-9},"fullHighlight":false,"groupWidth":0.7,"legend":{"calcs":[],"displayMode":"list","placement":"bottom","show":true,"showLegend":false},"orientation":"auto","rowsFrame":{"layout":"auto"},"showValue":"always","stacking":"none","tooltip":{"mode":"single","showColorScale":false,"sort":"none","yHistogram":false},"xTickLabelRotation":0,"xTickLabelSpacing":0,"yAxis":{"axisPlacement":"left","reverse":false}},"reverseYBuckets":false,"span":5,"targets":[{"expr":"topk([[topk]], sum(scylla_tablets_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(scylla_tablets_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by([[by]]))","format":"time_series","instant":false,"legendFormat":"{{dc}} {{instance}} {{shard}}","range":false,"refId":"A"}],"title":"Tablets over time per [[by]]","tooltip":{"show":true,"showHistogram":false},"type":"heatmap","xAxis":{"show":true},"xBucketNumber":null,"xBucketSize":null,"yAxis":{"decimals":null,"format":"short","logBase":1,"max":null,"min":null,"show":true,"splitFactor":null},"yBucketBound":"auto","yBucketNumber":null,"yBucketSize":null},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":23},"id":10,"panels":[],"repeat":"","title":"reads and writes","type":"row"},{"class":"plain_text","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":2,"w":24,"x":0,"y":24},"id":11,"isNew":true,"links":[],"options":{"content":"<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">Reads and Writes - Coordinator</h1>","mode":"html"},"pluginVersion":"7.4.2","span":12,"style":{},"title":"","transparent":true,"type":"text"},{"class":"writes_panel","datasource":"Prometheus","description":"Foreground writes are writes that weren't acknowledged yet to the application. For instance, if a single replica responded and two are needed due to the consistency level. This metric represents a queue size, not a rate. High values here correlate with increased write latencies.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":26},"id":12,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_storage_proxy_coordinator_foreground_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_storage_proxy_coordinator_foreground_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Foreground Writes per [[by]]","type":"timeseries"},{"class":"writes_panel","datasource":"Prometheus","description":"Background writes are writes that are already acknowledged to the application but have additional work to be done. For instance, if a replica responded and only one is needed, this request is still listed as a background request until all replicas respond. This metric represents a queue size, not a rate. High values here correlate with increased write latencies.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":26},"id":13,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_storage_proxy_coordinator_background_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_storage_proxy_coordinator_background_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Background Writes per [[by]]","type":"timeseries"},{"class":"reads_panel","datasource":"Prometheus","description":"Foreground reads are reads that weren't acknowledged yet to the application. For instance, if a single replica responded and two are needed due to the consistency level. This metric represents a queue size, not a rate. High values here correlate with increased read latencies.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":26},"id":14,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_storage_proxy_coordinator_foreground_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_storage_proxy_coordinator_foreground_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"Foreground Reads per [[by]]","type":"timeseries"},{"class":"reads_panel","datasource":"Prometheus","description":"Background reads are reads that are already acknowledged to the application but have additional work to be done. For instance, if a replica responded and only one is needed, this request is still listed as a background request until all replicas respond. This metric represents a queue size, not a rate. High values here correlate with increased read latencies.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":26},"id":15,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_storage_proxy_coordinator_background_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_storage_proxy_coordinator_background_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":4}],"title":"Background Reads per [[by]]","type":"timeseries"},{"class":"wps_panel","datasource":"Prometheus","description":"Number of successfully written hints.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":32},"id":16,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_hints_manager_written{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_hints_manager_written{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Hints Written per [[by]]","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"Number of sent hints.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":32},"id":17,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_hints_manager_sent{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_hints_manager_sent{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":4}],"title":"Hints sent per [[by]]","type":"timeseries"},{"class":"requestsps_panel","datasource":"Prometheus","description":"Holds an incrementing counter with the requests that were shed due to overload (threshold configured via max_concurrent_requests_per_shard). The first derivative of this value shows how often we shed requests due to overload in the \"CQL transport\" component.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:requests/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":32},"id":20,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_transport_requests_shed{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_transport_requests_shed{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Requests Shed","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"Number of times a read was done on behalf of a speculative retry.\n\nSpeculative retry is a mechanism that causes the client or server to speculate that a request may fail, and send a new request.\n\nspeculative retry may reduce latency in exchange for system load, but only if there is little activity.\n\nA lot of speculative retries increases load and can harm latency more than helping.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":32},"id":19,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_storage_proxy_coordinator_speculative_data_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_storage_proxy_coordinator_speculative_data_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":4}],"title":"Speculative Data Reads By [[by]]","type":"timeseries"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":38},"id":21,"panels":[],"repeat":"","title":"Latencies","type":"row"},{"class":"wps_panel","datasource":"Prometheus","description":"writes rate","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":39},"id":22,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_storage_proxy_coordinator_write_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]],scheduling_group_name) or on([[by]],scheduling_group_name) $func(rate(scylla_storage_proxy_coordinator_write_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]],scheduling_group_name)) or on ([[by]], scheduling_group_name) bottomk([[bottomk]], $func(rate(scylla_storage_proxy_coordinator_write_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]],scheduling_group_name) or on([[by]],scheduling_group_name) $func(rate(scylla_storage_proxy_coordinator_write_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]],scheduling_group_name))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Writes by [[by]]","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"Reads rate","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":39},"id":26,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_storage_proxy_coordinator_read_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]],scheduling_group_name) or on ([[by]],scheduling_group_name) $func(rate(scylla_storage_proxy_coordinator_read_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]],scheduling_group_name)) or on ([[by]],scheduling_group_name) bottomk([[bottomk]], $func(rate(scylla_storage_proxy_coordinator_read_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]],scheduling_group_name) or on ([[by]],scheduling_group_name) $func(rate(scylla_storage_proxy_coordinator_read_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]],scheduling_group_name))","intervalFactor":1,"legendFormat":"{{scheduling_group_name}} {{cluster}} {{dc}} {{instance}} {{shard}}","refId":"A","step":1}],"title":"Reads by [[by]]","type":"timeseries"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":45},"id":36,"panels":[],"repeat":"","title":"Replica","type":"row"},{"class":"plain_text","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":2,"w":24,"x":0,"y":46},"id":37,"isNew":true,"links":[],"options":{"content":"<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">Replica</h1>","mode":"html"},"pluginVersion":"7.4.2","span":12,"style":{},"title":"","transparent":true,"type":"text"},{"class":"reads_panel","datasource":"Prometheus","description":"The number of currently active read operations","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":48},"id":38,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_database_active_reads{instance=~\"[[node]]\",cluster=~\"$cluster|$^\", dc=~\"$dc\", shard=~\"[[shard]]\", class=~\"$sg\"}>0) by ([[by]], class)) or on ([[by]], class) bottomk([[bottomk]], $func(scylla_database_active_reads{instance=~\"[[node]]\",cluster=~\"$cluster|$^\", dc=~\"$dc\", shard=~\"[[shard]]\", class=~\"$sg\"}>0) by ([[by]], class))","intervalFactor":1,"legendFormat":"{{class}} {{dc}} {{instance}} {{shard}}","refId":"A","step":1}],"title":"Active reads","type":"timeseries"},{"class":"reads_panel","datasource":"Prometheus","description":"number of currently queued read operations","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":48},"id":39,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_database_queued_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_database_queued_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Queued reads","type":"timeseries"},{"class":"writes_panel","datasource":"Prometheus","description":"The current number of requests blocked due to reaching the memory quota. Non-zero value indicates that our bottleneck is memory","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":48},"id":40,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_database_requests_blocked_memory_current{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_database_requests_blocked_memory_current{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Writes currently blocked on dirty","type":"timeseries"},{"class":"writes_panel","datasource":"Prometheus","description":"number of currently pending allocations. A non-zero value indicates that we have a bottleneck in the disk write flow.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":48},"id":41,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_commitlog_pending_allocations{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_commitlog_pending_allocations{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Writes currently blocked on commitlog","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Reciprocal Miss Rate is a score in the range of 1 to 100 that is used to decide the fraction of read requests to send to each replica - a replica with twice the RMR value of another replica will serve twice the number of read requests.\n\nRMR is calculated on a table level, this is an aggregate estimation of that score.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":54},"id":42,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], clamp_max(1 + sum((rate(scylla_cache_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]) - rate(scylla_cache_reads_with_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]))) by ([[by]])/(sum(rate(scylla_cache_reads_with_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) + 0.00001),100)) or on ([[by]]) bottomk([[bottomk]], clamp_max(1 + sum((rate(scylla_cache_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]) - rate(scylla_cache_reads_with_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]))) by ([[by]])/(sum(rate(scylla_cache_reads_with_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) + 0.00001),100))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Reciprocal Miss Rate (HWLB)","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"Counts the total number of failed user read operations. Add the total_reads to this value to get the total amount of reads issued on this shard.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":54},"id":43,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_database_total_reads_failed{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_database_total_reads_failed{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Reads failed","type":"timeseries"},{"class":"wps_panel","datasource":"Prometheus","description":"Holds the current number of requests blocked due to reaching the memory quota (15566635008B). Non-zero value indicates that our bottleneck is memory and more specifically - the memory quota allocated for the \"database\" component.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":54},"id":44,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_database_requests_blocked_memory{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_database_requests_blocked_memory{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Writes blocked on dirty","type":"timeseries"},{"class":"wps_panel","datasource":"Prometheus","description":"Counts number of requests blocked due to memory pressure. A non-zero value indicates that the commitlog memory quota is not enough to serve the required amount of requests.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":54},"id":45,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_commitlog_requests_blocked_memory{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_commitlog_requests_blocked_memory{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Writes blocked on commitlog","type":"timeseries"},{"class":"text_panel","dashversion":["<2024.1",">5.0"],"datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":6,"w":12,"x":0,"y":60},"id":46,"isNew":true,"links":[],"options":{"content":"","mode":"markdown"},"pluginVersion":"7.4.2","span":6,"title":"","transparent":true,"type":"text"},{"class":"wps_panel","datasource":"Prometheus","description":"Counts the total number of failed write operations. A sum of this value plus total_writes represents a total amount of writes attempted on this shard.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":60},"id":47,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_database_total_writes_failed{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_database_total_writes_failed{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Writes failed","type":"timeseries"},{"class":"wps_panel","datasource":"Prometheus","description":"Counts write operations failed due to a timeout. A positive value is a sign of storage being overloaded.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":60},"id":48,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_database_total_writes_timedout{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_database_total_writes_timedout{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Writes timed out","type":"timeseries"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":66},"id":49,"panels":[],"repeat":"","title":"Storage","type":"row"},{"class":"graph_panel","datasource":"Prometheus","description":"Number of sstables currently open for reading \n\nscylla_sstables_currently_open_for_reading","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":67},"id":50,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_sstables_currently_open_for_reading{instance=~\"[[node]]\",cluster=~\"$cluster|$^\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_sstables_currently_open_for_reading{instance=~\"[[node]]\",cluster=~\"$cluster|$^\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Open sstables for reading","type":"timeseries"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":73},"id":53,"panels":[],"repeat":"","title":"Cache","type":"row"},{"class":"text_panel","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":2,"w":24,"x":0,"y":74},"id":54,"isNew":true,"links":[],"options":{"content":"<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">Cache - Replica</h1>","mode":"html"},"pluginVersion":"7.4.2","span":12,"style":{},"title":"","transparent":true,"type":"text"},{"class":"rps_panel","datasource":"Prometheus","description":"number of reads that were served from the cache","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":12,"x":0,"y":76},"id":55,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":6,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]) - rate(scylla_cache_reads_with_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]) - rate(scylla_cache_reads_with_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Reads with no misses","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"number of reads which had to read from sstables","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":12,"x":12,"y":76},"id":56,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":6,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_reads_with_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_reads_with_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Reads with misses","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"total number of rows needed by reads and found in cache","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":82},"id":57,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_row_hits{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_row_hits{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Row Hits","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"total number of rows needed by reads and missing in cache","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":82},"id":58,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_row_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_row_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Row Misses","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"number of partitions needed by reads and found in cache","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":82},"id":59,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_partition_hits{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_partition_hits{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Partition Hits","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"number of partitions needed by reads and missing in cache","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":82},"id":60,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_partition_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_partition_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Partition Misses","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"total number of rows added to cache","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":88},"id":61,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_row_insertions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_row_insertions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Row Insertions","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"total number of rows evicted from cache","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":88},"id":62,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_row_evictions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_row_evictions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Row Evictions","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"total number of partitions added to cache","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":88},"id":63,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_partition_insertions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_partition_insertions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Partition Insertions","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"total number of evicted partitions","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":88},"id":64,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_partition_evictions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_partition_evictions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Partition Evictions","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"total number of rows in memtables which were merged with existing rows during cache update on memtable flush","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":94},"id":65,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_rows_merged_from_memtable{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_rows_merged_from_memtable{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Row Merges","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"total number of invalidated rows","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":94},"id":66,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_row_removals{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_row_removals{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Row Removals","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"total number of partitions merged","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":94},"id":67,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_partition_merges{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_partition_merges{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Partition Merges","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"total number of invalidated partitions","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":94},"id":68,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_partition_removals{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_partition_removals{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Partition Removals","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"total number of cached rows","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":100},"id":69,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_cache_rows{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_cache_rows{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Rows","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"total number of cached partitions","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":100},"id":70,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_cache_partitions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_cache_partitions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Partitions","type":"timeseries"},{"class":"bytes_panel","datasource":"Prometheus","description":"current bytes used by the cache out of the total size of memory","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":100},"id":71,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_cache_bytes_used{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_cache_bytes_used{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Used Bytes","type":"timeseries"},{"class":"bytes_panel","datasource":"Prometheus","description":"total size of memory for the cache","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":100},"id":72,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_cache_bytes_total{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_cache_bytes_total{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Total Bytes","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Counts the number of prepared statements cache entries evictions.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":106},"id":73,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cql_prepared_cache_evictions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cql_prepared_cache_evictions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Prepared Statements Cache Eviction","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Counts the number of authenticated prepared statements cache entries evictions.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":106},"id":74,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cql_authorized_prepared_statements_cache_evictions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cql_authorized_prepared_statements_cache_evictions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Authorized Prepared Statements Cache Eviction","type":"timeseries"},{"class":"collapsible_row_panel","collapsed":true,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":112},"id":75,"panels":[{"class":"text_panel","content":"<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">Materialized Views - Replica</h1>","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":2,"w":24,"x":0,"y":140},"id":76,"isNew":true,"links":[],"mode":"html","options":{},"span":12,"style":{},"title":"","transparent":true,"type":"text"},{"class":"wps_panel","datasource":"Prometheus","description":"Number of view update locally","editable":true,"error":false,"fieldConfig":{"defaults":{"class":"fieldConfig_defaults","color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"class":"fieldConfig_defaults_custom","drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":142},"id":77,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_database_total_view_updates_pushed_local{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_database_total_view_updates_pushed_local{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"View Local Update","type":"timeseries"},{"class":"wps_panel","datasource":"Prometheus","description":"Number of view update remotely","editable":true,"error":false,"fieldConfig":{"defaults":{"class":"fieldConfig_defaults","color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"class":"fieldConfig_defaults_custom","drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":142},"id":78,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_database_total_view_updates_pushed_remote{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_database_total_view_updates_pushed_remote{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"View Remote Update","type":"timeseries"},{"class":"bytes_panel","datasource":"Prometheus","description":"Size in bytes of the view update backlog at each base replica.","editable":true,"error":false,"fieldConfig":{"defaults":{"class":"fieldConfig_defaults","color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"class":"fieldConfig_defaults_custom","drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"unit":"bytes"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":142},"id":79,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_database_view_update_backlog{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_database_view_update_backlog{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"View Update Backlog","type":"timeseries"},{"class":"ops_panel","datasource":"Prometheus","description":"Number of dropped view updates due to an excessive view update backlog.","editable":true,"error":false,"fieldConfig":{"defaults":{"class":"fieldConfig_defaults","color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"class":"fieldConfig_defaults_custom","drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":142},"id":80,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"}},"seriesOverrides":[{}],"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_database_dropped_view_updates{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_database_dropped_view_updates{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"Dropped View Updates","type":"timeseries"},{"class":"wps_panel","datasource":"Prometheus","description":"Number of hints sent for view.","editable":true,"error":false,"fieldConfig":{"defaults":{"class":"fieldConfig_defaults","color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"class":"fieldConfig_defaults_custom","drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":148},"id":81,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_hints_for_views_manager_sent{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_hints_for_views_manager_sent{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"Hints for view","type":"timeseries"},{"class":"writes_panel","datasource":"Prometheus","description":"Currently throttled base writes, as a consequence of the respective view update backlog.","editable":true,"error":false,"fieldConfig":{"defaults":{"class":"fieldConfig_defaults","color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"class":"fieldConfig_defaults_custom","drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"unit":"si:writes"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":148},"id":82,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(scylla_storage_proxy_coordinator_current_throttled_base_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"})  by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_storage_proxy_coordinator_current_throttled_base_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"})  by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"Throttled Base Writes","type":"timeseries"}],"repeat":"","title":"Materialized Views","type":"row"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":113},"id":83,"panels":[],"repeat":"","title":"Tombstones","type":"row"},{"class":"rps_panel","datasource":"Prometheus","description":"Amount of range tombstones processed during read.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":114},"id":84,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_sstables_range_tombstone_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_sstables_range_tombstone_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"Range Tombstones reads","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"Amount of range tombstones processed during read.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":114},"id":85,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_range_tombstone_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_range_tombstone_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"Cache Range Tombstones Read","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"Amount of row tombstones read","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":114},"id":86,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_sstables_row_tombstone_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_sstables_row_tombstone_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"Row Tombstones reads","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"Amount of cache row tombstones read","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":114},"id":87,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cache_row_tombstone_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cache_row_tombstone_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"Cache Row Tombstones reads","type":"timeseries"},{"class":"wps_panel","datasource":"Prometheus","description":"Amount of tombstones writes.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":120},"id":88,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_sstables_tombstone_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_sstables_tombstone_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"Tombstones Writes","type":"timeseries"},{"class":"wps_panel","datasource":"Prometheus","description":"Amount of range tombstones writes.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":120},"id":89,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_sstables_range_tombstone_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_sstables_range_tombstone_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"Range Tombstones Writes","type":"timeseries"},{"class":"wps_panel","datasource":"Prometheus","description":"Amount of Cell Tombstones Writes.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":120},"id":90,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_sstables_cell_tombstone_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_sstables_cell_tombstone_writes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval]))  by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"Cell Tombstones Writes","type":"timeseries"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":126},"id":116,"panels":[],"repeat":"","title":"CDC","type":"row"},{"class":"text_panel","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":2,"w":24,"x":0,"y":127},"id":117,"isNew":true,"links":[],"options":{"content":"<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">CDC - Replica</h1>","mode":"html"},"pluginVersion":"7.4.2","span":12,"style":{},"title":"","transparent":true,"type":"text"},{"class":"ops_panel","datasource":"Prometheus","description":"The rate of CDC operations.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:ops/s"},"overrides":[]},"gridPos":{"h":6,"w":8,"x":0,"y":129},"id":118,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":4,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_cdc_operations_total{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_cdc_operations_total{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":10}],"title":"CDC Operations","type":"timeseries"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":135},"id":120,"panels":[],"repeat":"","title":"Memory","type":"row"},{"class":"text_panel","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":2,"w":24,"x":0,"y":136},"id":121,"isNew":true,"links":[],"options":{"content":"<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">Memory - Replica</h1>","mode":"html"},"pluginVersion":"7.4.2","span":12,"style":{},"title":"","transparent":true,"type":"text"},{"class":"bytes_panel","datasource":"Prometheus","description":"Holds a current size of allocated memory in bytes.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":6,"w":8,"x":0,"y":138},"id":122,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":4,"targets":[{"expr":"topk([[topk]], $func(scylla_lsa_total_space_bytes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_lsa_total_space_bytes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"LSA total memory","type":"timeseries"},{"class":"bytes_panel","datasource":"Prometheus","description":"Holds a current amount of used non-LSA memory.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":6,"w":8,"x":8,"y":138},"id":123,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":4,"targets":[{"expr":"topk([[topk]], $func(scylla_lsa_non_lsa_used_space_bytes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_lsa_non_lsa_used_space_bytes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Non-LSA used memory","type":"timeseries"},{"class":"percentunit_panel","datasource":"Prometheus","description":"Bloom filter should take a small percentage of the total memory.\n\nscylla_sstables_bloom_filter_memory_size","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMax":1,"axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"percentunit"},"overrides":[]},"gridPos":{"h":6,"w":8,"x":16,"y":138},"id":124,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":4,"targets":[{"expr":"topk([[topk]], $func(scylla_sstables_bloom_filter_memory_size{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])/$func(scylla_memory_total_memory{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_sstables_bloom_filter_memory_size{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])/$func(scylla_memory_total_memory{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Bloom Filter memory usage (percentage)","type":"timeseries"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":144},"id":125,"panels":[],"repeat":"","title":"Compaction","type":"row"},{"class":"text_panel","datasource":"Prometheus","editable":true,"error":false,"fieldConfig":{"defaults":{"custom":{}},"overrides":[]},"gridPos":{"h":2,"w":24,"x":0,"y":145},"id":126,"isNew":true,"links":[],"options":{"content":"<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">Compaction - Replica</h1>","mode":"html"},"pluginVersion":"7.4.2","span":12,"style":{},"title":"","transparent":true,"type":"text"},{"class":"graph_panel_int","datasource":"Prometheus","description":"Holds the number of currently active compactions.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":8,"x":0,"y":147},"id":127,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":4,"targets":[{"expr":"topk([[topk]], $func(scylla_compaction_manager_compactions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(scylla_compaction_manager_compactions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"Running Compactions","type":"timeseries"},{"class":"percent_panel","datasource":"Prometheus","description":"Percentage of CPU time used by compaction","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"percent"},"overrides":[]},"gridPos":{"h":6,"w":8,"x":8,"y":147},"id":128,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"seriesOverrides":[{}],"span":4,"targets":[{"expr":"topk([[topk]], ($func(rate(scylla_scheduler_runtime_ms{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", group=\"compaction\"}[$__rate_interval])) by ([[by]]))/10) or on ([[by]]) bottomk([[bottomk]], ($func(rate(scylla_scheduler_runtime_ms{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", group=\"compaction\"}[$__rate_interval])) by ([[by]]))/10)","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"Compactions CPU Runtime","type":"timeseries"},{"class":"graph_panel","datasource":"Prometheus","description":"Shares assigned to the compaction","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":6,"w":8,"x":16,"y":147},"id":129,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"span":4,"targets":[{"expr":"topk([[topk]], avg(scylla_scheduler_shares{group=\"compaction\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], avg(scylla_scheduler_shares{group=\"compaction\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))","format":"time_series","intervalFactor":1,"refId":"A"}],"title":"Compactions Shares","type":"timeseries"},{"class":"collapsible_row_panel","collapsed":false,"datasource":"Prometheus","gridPos":{"h":1,"w":24,"x":0,"y":153},"id":130,"panels":[],"repeat":"sg","scopedVars":{"sg":{"selected":true,"text":"statement","value":"statement"}},"title":"Latencies - $sg","type":"row"},{"class":"wps_panel","datasource":"Prometheus","description":"The general write latency histogram","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:writes/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":154},"id":131,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"sg":{"selected":true,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_storage_proxy_coordinator_write_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]) or on([[by]]) $func(rate(scylla_storage_proxy_coordinator_write_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_storage_proxy_coordinator_write_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]) or on([[by]]) $func(rate(scylla_storage_proxy_coordinator_write_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Writes by [[by]]","type":"timeseries"},{"class":"bps_panel","dashversion":[">5.3",">2022.1"],"datasource":"Prometheus","description":"Average CQL message size (received)","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":154},"id":140,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"sg":{"selected":true,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_transport_cql_request_bytes{kind=~\"$kind\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])/sum(rate(scylla_transport_cql_requests_count{kind=~\"$kind\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_transport_cql_request_bytes{kind=~\"$kind\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])/sum(rate(scylla_transport_cql_requests_count{kind=~\"$kind\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"Average received payload size by [[by]]","type":"timeseries"},{"class":"bps_panel","dashversion":[">5.3",">2022.1"],"datasource":"Prometheus","description":"Bytes sent in CQL messages","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":154},"id":141,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"sg":{"selected":true,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_transport_cql_response_bytes{kind=~\"$kind\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_transport_cql_response_bytes{kind=~\"$kind\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"Response payload by [[by]]","type":"timeseries"},{"class":"bps_panel","dashversion":[">5.3",">2022.1"],"datasource":"Prometheus","description":"Average CQL message size (sent)","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":154},"id":142,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"sg":{"selected":true,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_transport_cql_response_bytes{kind=~\"$kind\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])/sum(rate(scylla_transport_cql_requests_count{kind=~\"$kind\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_transport_cql_response_bytes{kind=~\"$kind\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])/sum(rate(scylla_transport_cql_requests_count{kind=~\"$kind\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"Average response payload size by [[by]]","type":"timeseries"},{"class":"rps_panel","datasource":"Prometheus","description":"The general read latency histogram","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"si:reads/s"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":0,"y":160},"id":135,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"sg":{"selected":true,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], $func(rate(scylla_storage_proxy_coordinator_read_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]) or on ([[by]]) $func(rate(scylla_storage_proxy_coordinator_read_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], $func(rate(scylla_storage_proxy_coordinator_read_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]) or on ([[by]]) $func(rate(scylla_storage_proxy_coordinator_read_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","refId":"A","step":1}],"title":"Reads by [[by]]","type":"timeseries"},{"class":"bytes_panel","dashversion":[">5.3",">2022.1"],"datasource":"Prometheus","description":"This is a ballpark estimation of the read-messages size (like select).\n\nIt is based on the assumption that read-messages are responsible for most outbound traffic.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":6,"y":160},"id":144,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"sg":{"selected":true,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_transport_cql_response_bytes{kind=~\"QUERY|EXECUTE\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])/(sum(rate(scylla_storage_proxy_coordinator_read_latency_summary_count{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]) or on ([[by]]) sum(rate(scylla_storage_proxy_coordinator_read_latency_count{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]))) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_transport_cql_response_bytes{kind=~\"QUERY|EXECUTE\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])/(sum(rate(scylla_storage_proxy_coordinator_read_latency_summary_count{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]) or on ([[by]]) sum(rate(scylla_storage_proxy_coordinator_read_latency_count{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])))","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"Estimated read message size by [[by]]","type":"timeseries"},{"class":"bps_panel","dashversion":[">5.3",">2022.1"],"datasource":"Prometheus","description":"Bytes received in CQL messages","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":12,"y":160},"id":139,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"sg":{"selected":true,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_transport_cql_request_bytes{kind=~\"$kind\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_transport_cql_request_bytes{kind=~\"$kind\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]))","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"Received payload by [[by]]","type":"timeseries"},{"class":"bytes_panel","dashversion":[">5.3",">2022.1"],"datasource":"Prometheus","description":"This is a ballpark estimation of the write-messages size (like insert and update).\n\nIt is based on the assumption that write-messages are responsible for most inwards traffic.","editable":true,"error":false,"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisLabel":"","axisPlacement":"auto","axisSoftMin":0,"barAlignment":0,"drawStyle":"line","fillOpacity":0,"gradientMode":"none","hideFrom":{"graph":false,"legend":false,"tooltip":false,"viz":false},"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{},"thresholdsStyle":{}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":6,"w":6,"x":18,"y":160},"id":143,"isNew":true,"links":[],"options":{"legend":{"calcs":[],"displayMode":"hidden","placement":"bottom"},"tooltip":{"maxHeight":600,"mode":"multi","sort":"desc"},"tooltipOptions":{"mode":"single"}},"scopedVars":{"sg":{"selected":true,"text":"statement","value":"statement"}},"span":3,"targets":[{"expr":"topk([[topk]], sum(rate(scylla_transport_cql_request_bytes{kind=~\"QUERY|EXECUTE\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])/(sum(rate(scylla_storage_proxy_coordinator_write_latency_summary_count{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]) or on([[by]]) sum(rate(scylla_storage_proxy_coordinator_write_latency_count{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]))) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_transport_cql_request_bytes{kind=~\"QUERY|EXECUTE\",instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])/(sum(rate(scylla_storage_proxy_coordinator_write_latency_summary_count{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]) or on([[by]]) sum(rate(scylla_storage_proxy_coordinator_write_latency_count{instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])))","intervalFactor":1,"legendFormat":"","metric":"","refId":"A","step":1}],"title":"Estimated write message size by [[by]]","type":"timeseries"}],"refresh":"30s","schemaVersion":27,"style":"dark","tags":["6.0.1"],"templating":{"list":[{"allValue":null,"class":"by_template_var","current":{"tags":[],"text":"Instance","value":"instance"},"description":null,"error":null,"hide":0,"includeAll":false,"label":"by","multi":false,"name":"by","options":[{"selected":false,"text":"Cluster","value":"cluster"},{"selected":false,"text":"DC","value":"dc"},{"selected":true,"text":"Instance","value":"instance"},{"selected":false,"text":"Shard","value":"instance,shard"}],"query":"Cluster : cluster,DC : dc, Instance : instance, Shard : instance\\,shard","skipUrlSync":false,"type":"custom"},{"allValue":null,"class":"template_variable_single","current":{"selected":false,"text":"EGT_ScyllaDB_Cluster_DEV","value":"EGT_ScyllaDB_Cluster_DEV"},"datasource":"Prometheus","definition":"","description":null,"error":null,"hide":0,"includeAll":false,"label":"cluster","multi":false,"name":"cluster","options":[],"query":{"query":"label_values(scylla_reactor_utilization, cluster)","refId":"Prometheus-cluster-Variable-Query"},"refresh":2,"regex":"","skipUrlSync":false,"sort":1,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":".*","class":"template_variable_all","current":{"selected":true,"text":["All"],"value":["$__all"]},"datasource":"Prometheus","definition":"","description":null,"error":null,"hide":0,"includeAll":true,"label":"dc","multi":true,"name":"dc","options":[],"query":{"query":"label_values(scylla_reactor_utilization{cluster=~\"$cluster\"}, dc)","refId":"Prometheus-dc-Variable-Query"},"refresh":2,"regex":"","skipUrlSync":false,"sort":1,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":".*","class":"template_variable_all","current":{"selected":true,"text":["All"],"value":["$__all"]},"datasource":"Prometheus","definition":"","description":null,"error":null,"hide":0,"includeAll":true,"label":"node","multi":true,"name":"node","options":[],"query":{"query":"label_values(scylla_reactor_utilization{cluster=\"$cluster\", dc=~\"$dc\"}, instance)","refId":"Prometheus-node-Variable-Query"},"refresh":2,"regex":"","skipUrlSync":false,"sort":1,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":".+","class":"template_variable_all","current":{"selected":true,"text":["All"],"value":["$__all"]},"datasource":"Prometheus","definition":"","description":null,"error":null,"hide":0,"includeAll":true,"label":"shard","multi":true,"name":"shard","options":[],"query":{"query":"label_values(scylla_reactor_utilization,shard)","refId":"Prometheus-shard-Variable-Query"},"refresh":2,"regex":"","skipUrlSync":false,"sort":3,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":".*","class":"template_variable_all","current":{"selected":true,"text":["statement"],"value":["statement"]},"datasource":"Prometheus","definition":"","description":null,"error":null,"hide":0,"includeAll":true,"label":"SG","multi":true,"name":"sg","options":[],"query":{"query":"label_values(scylla_scheduler_runtime_ms{cluster=\"$cluster\"}, group)","refId":"Prometheus-sg-Variable-Query"},"refresh":2,"regex":"","skipUrlSync":false,"sort":3,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":".*","class":"template_variable_all","current":{"selected":true,"text":["All"],"value":["$__all"]},"dashversion":[">5.3",">2022.1"],"datasource":"Prometheus","definition":"","description":null,"error":null,"hide":0,"includeAll":true,"label":"cql_kind","multi":true,"name":"kind","options":[],"query":{"query":"label_values(scylla_transport_cql_requests_count{cluster=\"$cluster\"}, kind)","refId":"Prometheus-kind-Variable-Query"},"refresh":2,"regex":"","skipUrlSync":false,"sort":3,"tagValuesQuery":"","tags":[],"tagsQuery":"","type":"query","useTags":false},{"allValue":null,"class":"aggregation_function","current":{"tags":[],"text":"sum","value":"sum"},"description":null,"error":null,"hide":0,"includeAll":false,"label":"Function","multi":false,"name":"func","options":[{"selected":true,"text":"sum","value":"sum"},{"selected":false,"text":"avg","value":"avg"},{"selected":false,"text":"max","value":"max"},{"selected":false,"text":"min","value":"min"},{"selected":false,"text":"stddev","value":"stddev"},{"selected":false,"text":"stdvar","value":"stdvar"}],"query":"sum,avg,max,min,stddev,stdvar","skipUrlSync":false,"type":"custom"},{"allValue":null,"class":"topk_limit","current":{"tags":[],"text":"256","value":"256"},"description":null,"error":null,"hide":0,"includeAll":false,"label":"Filter Highest","multi":false,"name":"topk","options":[{"selected":false,"text":"0","value":"0"},{"selected":false,"text":"5","value":"5"},{"selected":false,"text":"10","value":"10"},{"selected":false,"text":"20","value":"20"},{"selected":false,"text":"50","value":"50"},{"selected":false,"text":"100","value":"100"},{"selected":true,"text":"256","value":"256"},{"selected":false,"text":"512","value":"512"},{"selected":false,"text":"1000","value":"1000"},{"selected":false,"text":"10000","value":"10000"}],"query":"0, 5, 10, 20, 50, 100, 256, 500, 1000, 10000","skipUrlSync":false,"type":"custom"},{"allValue":null,"class":"botomk_limit","current":{"tags":[],"text":"0","value":"0"},"description":null,"error":null,"hide":0,"includeAll":false,"label":"Filter Lowest","multi":false,"name":"bottomk","options":[{"selected":true,"text":"0","value":"0"},{"selected":false,"text":"5","value":"5"},{"selected":false,"text":"10","value":"10"},{"selected":false,"text":"20","value":"20"},{"selected":false,"text":"50","value":"50"},{"selected":false,"text":"100","value":"100"},{"selected":true,"text":"256","value":"256"},{"selected":false,"text":"512","value":"512"},{"selected":false,"text":"1000","value":"1000"},{"selected":false,"text":"10000","value":"10000"}],"query":"0, 5, 10, 20, 50, 100, 256, 500, 1000, 10000","skipUrlSync":false,"type":"custom"},{"allValue":null,"class":"template_variable_custom","current":{"text":"6.0.1","value":"6.0.1"},"description":null,"error":null,"hide":2,"includeAll":false,"label":null,"multi":false,"name":"scylla_version","options":[{"selected":true,"text":"6.0.1","value":"6.0.1"}],"query":"6.0.1","skipUrlSync":false,"type":"custom"},{"allValue":null,"class":"monitor_version_var","current":{"text":"master","value":"master"},"description":null,"error":null,"hide":2,"includeAll":false,"label":null,"multi":false,"name":"monitoring_version","options":[{"selected":true,"text":"master","value":"master"}],"query":"master","skipUrlSync":false,"type":"custom"}]},"time":{"from":"now-30m","to":"now"},"timepicker":{"now":true,"refresh_intervals":["5s","10s","30s","1m","5m","15m","30m","1h","2h","1d"],"time_options":["5m","15m","1h","6h","12h","24h","2d","7d","30d"]},"timezone":"utc","title":"ScyllaDB Detailed","uid":"scylla-detailed-6-0-1","version":2414}