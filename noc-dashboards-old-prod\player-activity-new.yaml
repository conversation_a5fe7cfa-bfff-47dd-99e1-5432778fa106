apiVersion: v1
kind: ConfigMap
metadata:
  name: player-activity-new
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: NOC-dashboards
data:
  player-activity-new.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 721,
      "iteration": 1726123537642,
      "links": [],
      "panels": [
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "gridPos": {
            "h": 3,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 25,
          "options": {
            "content": "***<center>${bu}</center>***",
            "mode": "markdown"
          },
          "pluginVersion": "7.4.2",
          "timeFrom": null,
          "timeShift": null,
          "title": "Currently Looking At",
          "type": "text"
        },
        {
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 3,
            "w": 8,
            "x": 0,
            "y": 3
          },
          "id": 14,
          "options": {
            "colorMode": "value",
            "graphMode": "none",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "text": {
              "valueSize": 40
            },
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "format": "table",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "select\n    sum(count_id)\nFrom monitoring_player_registrations\nwhere $__timeFilter(create_date) and business_unit IN ($bu)",
              "refId": "B",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "New registrations",
          "type": "stat"
        },
        {
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 3,
            "w": 8,
            "x": 8,
            "y": 3
          },
          "id": 20,
          "options": {
            "colorMode": "value",
            "graphMode": "none",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "last"
              ],
              "fields": "",
              "values": false
            },
            "text": {
              "valueSize": 40
            },
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "format": "table",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  sum(player_count) AS \"Total\"\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) and business_unit IN ($bu)\nGROUP BY 1\nORDER BY 1",
              "refId": "B",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Current active players",
          "type": "stat"
        },
        {
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 3,
            "w": 8,
            "x": 16,
            "y": 3
          },
          "id": 17,
          "options": {
            "colorMode": "value",
            "graphMode": "none",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "max"
              ],
              "fields": "",
              "values": false
            },
            "text": {
              "valueSize": 40
            },
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "format": "table",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  sum(player_count) AS \"Total\"\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) and business_unit IN ($bu)\nGROUP BY 1\nORDER BY 1",
              "refId": "B",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Maximum active players",
          "type": "stat"
        },
        {
          "datasource": "PostgreSQL DWH",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": []
              },
              "unit": "none"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "0"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Sport"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Interactive"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Amatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Casino Technology (direct)"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "14"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Habanero"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "2"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Digital Games"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "22"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Scientific Games Digital"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evolution"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "4"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playson"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "41"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "7Mojos"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Spribe"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "7"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evoplay"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Pragmatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "36"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playtech"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "42"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Red Tiger"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "43"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 3,
            "w": 6,
            "x": 0,
            "y": 6
          },
          "id": 7,
          "options": {
            "colorMode": "value",
            "graphMode": "none",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "text": {
              "valueSize": 40
            },
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "format": "table",
              "group": [],
              "hide": false,
              "metricColumn": "business_unit",
              "rawQuery": true,
              "rawSql": "select count(distinct player_id)\nfrom reporting.sport_player_dimension_aggregation\nwhere\n$__timeFilter(date)\nand $__timeFilter(updated_date) \nand business_unit IN ($bu)",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "player_count"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "player_activity",
              "timeColumn": "activity_time",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Unique users with bets, SPORT",
          "transformations": [],
          "type": "stat"
        },
        {
          "datasource": "PostgreSQL DWH",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": []
              },
              "unit": "none"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "0"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Sport"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Interactive"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Amatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Casino Technology (direct)"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "14"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Habanero"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "2"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Digital Games"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "22"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Scientific Games Digital"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evolution"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "4"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playson"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "41"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "7Mojos"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Spribe"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "7"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evoplay"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Pragmatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "36"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playtech"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "42"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Red Tiger"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "43"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 3,
            "w": 6,
            "x": 6,
            "y": 6
          },
          "id": 6,
          "options": {
            "colorMode": "value",
            "graphMode": "none",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "text": {
              "valueSize": 40
            },
            "textMode": "value"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "format": "table",
              "group": [],
              "hide": false,
              "metricColumn": "business_unit",
              "rawQuery": true,
              "rawSql": "select count(distinct player_id)\nfrom reporting.casino_player_dimension_aggregation\nwhere $__timeFilter(updated_date) \nand $__timeFilter(date) \nand not is_live \nand business_unit IN ($bu)",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "player_count"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "player_activity",
              "timeColumn": "activity_time",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Unique users with bets, CASINO",
          "transformations": [],
          "type": "stat"
        },
        {
          "datasource": "PostgreSQL DWH",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": []
              },
              "unit": "none"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "0"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Sport"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Interactive"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Amatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Casino Technology (direct)"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "14"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Habanero"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "2"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Digital Games"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "22"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Scientific Games Digital"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evolution"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "4"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playson"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "41"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "7Mojos"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Spribe"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "7"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evoplay"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Pragmatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "36"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playtech"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "42"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Red Tiger"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "43"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 3,
            "w": 6,
            "x": 12,
            "y": 6
          },
          "id": 4,
          "options": {
            "colorMode": "value",
            "graphMode": "none",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "text": {
              "valueSize": 40
            },
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "format": "table",
              "group": [],
              "hide": false,
              "metricColumn": "business_unit",
              "rawQuery": true,
              "rawSql": "select count(distinct player_id)\nfrom reporting.casino_player_dimension_aggregation\nwhere \n$__timeFilter(date) \nand $__timeFilter(updated_date) \nand is_live \nand business_unit IN ($bu)",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Unique users with bets, Live CASINO",
          "transformations": [],
          "type": "stat"
        },
        {
          "datasource": "PostgreSQL DWH",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": []
              },
              "unit": "none"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 3,
            "w": 6,
            "x": 18,
            "y": 6
          },
          "id": 8,
          "options": {
            "colorMode": "value",
            "graphMode": "none",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "text": {
              "valueSize": 40
            },
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "format": "table",
              "group": [],
              "hide": false,
              "metricColumn": "business_unit",
              "rawQuery": true,
              "rawSql": "with abc as (select distinct player_id\n             from reporting.casino_player_dimension_aggregation\n             where $__timeFilter(updated_date) and $__timeFilter(date) and business_unit IN ($bu)\n                union\n             select distinct player_id\n             from reporting.sport_player_dimension_aggregation\n             where $__timeFilter(updated_date) and $__timeFilter(date) and business_unit IN ($bu)\n            ) select count(*) from abc;",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "player_count"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "player_activity",
              "timeColumn": "activity_time",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Unique users with bets, Total Players",
          "transformations": [],
          "type": "stat"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "none"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Amusnet Interactive (EGTI)"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "2"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Digital Games"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evolution"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "4"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playson"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Spribe"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "7"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evoplay"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Pragmatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "9"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Endorphina"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Amatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Casino Technology"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "14"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Habanero"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "22"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Scientific Games Digital"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "36"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playtech"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "38"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "BeeFee"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "41"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "7Mojos"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "42"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Red Tiger"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "43"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "46"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "iWin"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "30"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Skywind"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "56"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "ELK (SGD)"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "37"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Digital Scratch Games"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "33"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Wazdan"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "32"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Fazi"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "65"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "BTG"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "48"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Spinomenal"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "72"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Microgaming"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "57"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "ELK Studio (Direct)"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "45"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "World Match"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "51"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Synot"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "64"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "No Limit City"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "81"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Reevo"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "5"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "iSoftBet"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "39"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "GreenTube"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "52"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Kajot"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "71"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Hacksaw (Pariplay)"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "31"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Nsoft"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "53"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Yggdrasil"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "28"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "RedRake"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "50"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Apollo"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "54"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Ezugi"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "44"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Gameart"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "83"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Expanse"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "59"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "BGaming"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "86"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Kalamba (Pariplay)"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "55"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Betixon"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "16"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Gamomat (Oryx)"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "62"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "TomHorn"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "16"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Pariplay"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "89"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "GGames"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "95"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Caleta"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "91"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Threeoaks"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "63"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Barbara Bang"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "58"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "KaGaming"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "40"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Pariplay"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 24,
            "x": 0,
            "y": 9
          },
          "hiddenSeries": false,
          "id": 12,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": true,
            "hideZero": true,
            "max": true,
            "min": true,
            "rightSide": true,
            "show": true,
            "sideWidth": 400,
            "sort": "min",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [
            {
              "alias": "Total",
              "yaxis": 1
            }
          ],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id != 2 AND game_provider_id != 1 AND game_provider_id != 0 and business_unit IN ($bu)\nORDER BY 1,2",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  sum(player_count) AS \"Total\"\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id != 0 and business_unit IN ($bu)\nGROUP BY 1\nORDER BY 1",
              "refId": "B",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id = 2 and business_unit IN ($bu)\nORDER BY 1,2",
              "refId": "C",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id = 1 and business_unit IN ($bu)\nORDER BY 1,2",
              "refId": "D",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Unique users with bets per minute, CASINO",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:2401",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:2402",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "none"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "0"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Sport"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 24,
            "x": 0,
            "y": 17
          },
          "hiddenSeries": false,
          "id": 23,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": true,
            "max": true,
            "min": true,
            "rightSide": true,
            "show": true,
            "sideWidth": 400,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id = 0 and business_unit IN ($bu)\nORDER BY 1,2",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Unique users with bets per minute, SPORT",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:2401",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:2402",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "5m",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {
              "selected": true,
              "tags": [],
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": "audit-logs-monitoring-db",
            "definition": "SELECT DISTINCT business_unit FROM monitoring_player_registrations",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": null,
            "multi": true,
            "name": "bu",
            "options": [],
            "query": "SELECT DISTINCT business_unit FROM monitoring_player_registrations",
            "refresh": 2,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-7d",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "NOC - Player Activity",
      "uid": "y6Q0XaU712new",
      "version": 2
    }