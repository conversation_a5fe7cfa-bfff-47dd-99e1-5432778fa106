apiVersion: v1
kind: ConfigMap
metadata:
  name: aggregations-alerts-dev
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: infrastructure-dev
data:
  aggregations-alerts-dev.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 277,
      "links": [],
      "panels": [
        {
          "alert": {
            "alertRuleTags": { "severity": "critical" },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    15
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "5m",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "avg"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "5m",
            "frequency": "1m",
            "handler": 1,
            "name": "Aggregations slow alert DEV with severity",
            "noDataState": "alerting",
            "notifications": []
          },
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "PostgreSQL-Dev",
          "description": "It is running more than 15 min",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 4,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"time\",\n  DATE_PART('minute', exec_done::timestamp - exec_start::timestamp) AS \"DATE_PART\"\nFROM reporting.stat_aggregate AS \"a\"\nWHERE\n  \"a\".\"date\" BETWEEN NOW() - INTERVAL '24 HOURS' AND NOW()\nORDER BY time desc",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "double_"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "act_hi_dec_in",
              "timeColumn": "create_time_",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 15,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Aggregations slow",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:1539",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:1540",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": { "severity": "critical" },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    0
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "5m",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "avg"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "5m",
            "frequency": "1m",
            "handler": 1,
            "name": "Aggregations error alert DEV",
            "noDataState": "alerting",
            "notifications": []
          },
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "PostgreSQL-Dev",
          "description": "Query has error",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 24,
            "x": 0,
            "y": 8
          },
          "hiddenSeries": false,
          "id": 5,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"time\",\n  has_errors::int AS \"has_errors\"\nFROM reporting.stat_aggregate AS \"a\"\nWHERE\n  \"a\".\"date\" BETWEEN NOW() - INTERVAL '24 HOURS' AND NOW()\nORDER BY time desc",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "double_"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "act_hi_dec_in",
              "timeColumn": "create_time_",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 0,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Aggregations error",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:1539",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:1540",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "datasource": "PostgreSQL-Dev",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": null,
                "filterable": false
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "proc"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 361
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "id"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 59
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "hour"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 75
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "args"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 127
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "has_errors"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 98
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "date"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 185
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "exec_start"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 235
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "exec_done"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 230
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "exec time"
                },
                "properties": [
                  {
                    "id": "custom.width",
                    "value": 94
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 10,
            "w": 24,
            "x": 0,
            "y": 16
          },
          "id": 2,
          "options": {
            "showHeader": true,
            "sortBy": []
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "format": "table",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"date\",\n  id,\n  proc,\n  args,\n  hour,\n  exec_start,\n  exec_done,\n  DATE_PART('minute', exec_done::timestamp - exec_start::timestamp) AS \"exec time\",\n  has_errors,\n  error\nFROM reporting.stat_aggregate\nORDER BY exec_start desc",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "double_"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "act_hi_dec_in",
              "timeColumn": "create_time_",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "title": "Table",
          "type": "table"
        }
      ],
      "refresh": "10s",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-24h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Aggregations alerts",
      "uid": "FCB5qQJ7dev",
      "version": 6
    }