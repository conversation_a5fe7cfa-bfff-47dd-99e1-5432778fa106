{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "PostgreSQL", "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "metricColumn": "business_unit", "rawQuery": true, "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time)\nORDER BY 1,2", "refId": "A", "select": [[{"params": ["player_count"], "type": "column"}]], "table": "player_activity", "timeColumn": "activity_time", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Panel Title", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:255", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:256", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "PostgreSQL", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 14, "w": 24, "x": 0, "y": 8}, "id": 2, "options": {"showHeader": true}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n*\nFROM player_activity", "refId": "A", "select": [[{"params": ["redis_wait"], "type": "column"}]], "table": "sport_offering_timing", "timeColumn": "date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Panel Title", "type": "table"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "2022-04-08T14:13:00.000Z", "to": "2022-04-08T14:17:00.000Z"}, "timepicker": {}, "timezone": "", "title": "Player Activity", "uid": null, "version": 0}