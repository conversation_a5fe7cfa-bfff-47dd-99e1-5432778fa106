---
apiVersion: v1
kind: ConfigMap
metadata:
  name: scylla-keyspaces-dashboard
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: ScyllaDB
data:
  scylla-keyspaces-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": {
              "type": "datasource",
              "uid": "grafana"
            },
            "enable": false,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          },
          {
            "class": "annotation_restart",
            "datasource": {
              "type": "prometheus",
              "uid": "prometheus"
            },
            "enable": true,
            "expr": "resets(scylla_gossip_heart_beat{cluster=\"$cluster\"}[$__rate_interval])>0",
            "hide": false,
            "iconColor": "rgba(255, 96, 96, 1)",
            "limit": 100,
            "name": "node_restart",
            "showIn": 0,
            "tagKeys": "instance,dc,cluster",
            "tags": [],
            "titleFormat": "restart",
            "type": "tags"
          },
          {
            "class": "annotation_stall",
            "datasource": {
              "type": "prometheus",
              "uid": "prometheus"
            },
            "enable": false,
            "expr": "changes(scylla_stall_detector_reported{cluster=\"$cluster\"}[$__rate_interval])>0",
            "hide": false,
            "iconColor": "rgba(255, 96, 96, 1)",
            "limit": 100,
            "name": "stall detector",
            "showIn": 0,
            "tagKeys": "dc,instance,shard",
            "tags": [],
            "titleFormat": "Stall found",
            "type": "tags"
          },
          {
            "class": "annotation_schema_changed",
            "datasource": {
              "type": "prometheus",
              "uid": "prometheus"
            },
            "enable": false,
            "expr": "changes(scylla_database_schema_changed{cluster=\"$cluster\"}[$__rate_interval])>0",
            "hide": false,
            "iconColor": "rgba(255, 96, 96, 1)",
            "limit": 100,
            "name": "Schema Changed",
            "showIn": 0,
            "tagKeys": "instance,dc,cluster",
            "tags": [],
            "titleFormat": "schema changed",
            "type": "tags"
          },
          {
            "class": "annotation_manager_task",
            "datasource": {
              "type": "prometheus",
              "uid": "prometheus"
            },
            "enable": true,
            "expr": "scylla_manager_task_active_count{type=~\"repair|backup\",cluster=\"$cluster\"}>0",
            "hide": false,
            "iconColor": "#73BF69",
            "limit": 100,
            "name": "Task",
            "showIn": 0,
            "tagKeys": "type",
            "tags": [],
            "titleFormat": "Running",
            "type": "tags"
          },
          {
            "class": "annotation_hints_writes",
            "datasource": {
              "type": "prometheus",
              "uid": "prometheus"
            },
            "enable": false,
            "expr": "changes(scylla_hints_manager_written{cluster=\"$cluster\"}[$__rate_interval])>0",
            "hide": false,
            "iconColor": "rgb(255, 176, 0, 128)",
            "limit": 100,
            "name": "Hints Write",
            "showIn": 0,
            "tagKeys": "instance,dc,cluster",
            "tags": [],
            "titleFormat": "Hints write",
            "type": "tags"
          },
          {
            "class": "annotation_hints_sent",
            "datasource": {
              "type": "prometheus",
              "uid": "prometheus"
            },
            "enable": false,
            "expr": "changes(scylla_hints_manager_sent{cluster=\"$cluster\"}[$__rate_interval])>0",
            "hide": false,
            "iconColor": "rgb(50, 176, 0, 128)",
            "limit": 100,
            "name": "Hints Sent",
            "showIn": 0,
            "tagKeys": "instance,dc,cluster",
            "tags": [],
            "titleFormat": "Hints Sent",
            "type": "tags"
          },
          {
            "class": "mv_building",
            "datasource": {
              "type": "prometheus",
              "uid": "prometheus"
            },
            "enable": true,
            "expr": "sum(scylla_view_builder_builds_in_progress{cluster=\"$cluster\"})>0",
            "hide": false,
            "iconColor": "rgb(50, 176, 0, 128)",
            "limit": 100,
            "name": "MV",
            "showIn": 0,
            "tagKeys": "instance,dc,cluster",
            "tags": [],
            "titleFormat": "Materialized View built",
            "type": "tags"
          },
          {
            "class": "ops_annotation",
            "datasource": {
              "type": "prometheus",
              "uid": "prometheus"
            },
            "enable": true,
            "expr": "10*min(scylla_node_ops_finished_percentage{cluster=\"$cluster\"}) by (ops, dc,instance) < 10",
            "hide": false,
            "iconColor": "rgb(50, 176, 0, 128)",
            "limit": 100,
            "name": "ops",
            "showIn": 0,
            "tagKeys": "ops,dc,instance",
            "tags": [],
            "titleFormat": "Operation",
            "type": "tags"
          },
          {
            "class": "stream_annotation",
            "dashversion": [
              ">5.2",
              ">2023.1"
            ],
            "datasource": {
              "type": "prometheus",
              "uid": "prometheus"
            },
            "enable": true,
            "expr": "10*min(scylla_streaming_finished_percentage{cluster=\"$cluster\"}) by (ops, dc,instance) < 10",
            "hide": false,
            "iconColor": "rgb(50, 176, 0, 128)",
            "limit": 100,
            "name": "streaming",
            "showIn": 0,
            "tagKeys": "ops,dc,instance",
            "tags": [],
            "titleFormat": "Streaming",
            "type": "tags"
          }
        ]
      },
      "editable": true,
      "fiscalYearStartMonth": 0,
      "graphTooltip": 1,
      "id": 4642,
      "links": [
        {
          "asDropdown": true,
          "icon": "external link",
          "includeVars": true,
          "keepTime": true,
          "tags": [],
          "type": "dashboards"
        }
      ],
      "panels": [
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "custom": {
                "align": "auto",
                "cellOptions": {
                  "type": "auto"
                },
                "filterable": true,
                "inspect": false
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 2,
          "options": {
            "cellHeight": "sm",
            "footer": {
              "countRows": false,
              "fields": "",
              "reducer": [
                "sum"
              ],
              "show": false
            },
            "showHeader": true
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(scylla_column_family_live_sstable{ks=\"$ks\", cf=~\"$table\"}) by(ks,cf)",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(rate(scylla_column_family_write_latency_count{ks=\"$ks\", cf=~\"$table\"}[1m])) by( ks,cf)",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "wlatencyp95ks{ks=\"$ks\", cf=~\"$table\", by=\"cluster\", cluster=\"$cluster\"}",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "C"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "wlatencyp99ks{ks=\"$ks\", cf=~\"$table\", by=\"cluster\", cluster=\"$cluster\"}",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "D"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(rate(scylla_column_family_read_latency_count{ks=\"$ks\", cf=~\"$table\"}[1m])) by( ks,cf)",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "E"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "rlatencyp95ks{ks=\"$ks\", cf=~\"$table\", by=\"cluster\", cluster=\"$cluster\"}",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "F"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "rlatencyp99ks{ks=\"$ks\", cf=~\"$table\", by=\"cluster\", cluster=\"$cluster\"}",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "G"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(scylla_column_family_total_disk_space{ks=\"$ks\", cf=~\"$table\", cluster=\"$cluster\"}) by (cf)",
              "format": "table",
              "hide": false,
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "H"
            }
          ],
          "title": "Keyspace tables",
          "transformations": [
            {
              "id": "joinByField",
              "options": {
                "byField": "cf",
                "mode": "outer"
              }
            },
            {
              "id": "filterFieldsByName",
              "options": {
                "include": {
                  "names": [
                    "ks 1",
                    "Value #A",
                    "Value #B",
                    "Value #C",
                    "Value #D",
                    "Value #E",
                    "Value #F",
                    "Value #G",
                    "Value #H",
                    "cf"
                  ]
                }
              }
            },
            {
              "id": "organize",
              "options": {
                "excludeByName": {},
                "includeByName": {},
                "indexByName": {
                  "Value #A": 3,
                  "Value #B": 4,
                  "Value #C": 5,
                  "Value #D": 6,
                  "Value #E": 7,
                  "Value #F": 8,
                  "Value #G": 9,
                  "Value #H": 2,
                  "cf": 1,
                  "ks 1": 0
                },
                "renameByName": {
                  "Value #A": "sstables",
                  "Value #B": "writes/2",
                  "Value #C": "W P95",
                  "Value #D": "W P99",
                  "Value #E": "reads/2",
                  "Value #F": "R P95",
                  "Value #G": "R P99",
                  "Value #H": "Disk space",
                  "cf": "table",
                  "ks 1": "keyspace"
                }
              }
            }
          ],
          "type": "table"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 8
          },
          "id": 3,
          "panels": [],
          "repeat": "table",
          "title": "$ks:$table",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "si:writes/s"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 0,
            "y": 9
          },
          "id": 4,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "$func(rate(scylla_column_family_write_latency_count{ks=\"$ks\", cf=\"$table\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]])",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A",
              "step": 1
            }
          ],
          "title": "Writes by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "µs"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 6,
            "y": 9
          },
          "id": 5,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "wlatencyaks{ks=\"$ks\", cf=\"$table\", by=\"[[by]]\", instance=~\"[[node]]|$^\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A",
              "step": 1
            }
          ],
          "title": "Average write latency by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "µs"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 12,
            "y": 9
          },
          "id": 6,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "wlatencyp95ks{ks=\"$ks\", cf=\"$table\", by=\"[[by]]\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"} ",
              "intervalFactor": 1,
              "legendFormat": "",
              "metric": "",
              "refId": "A",
              "step": 1
            }
          ],
          "title": "95th percentile write latency by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "µs"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 18,
            "y": 9
          },
          "id": 7,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "wlatencyp99ks{ks=\"$ks\", cf=\"$table\", by=\"[[by]]\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"} ",
              "intervalFactor": 1,
              "legendFormat": "",
              "metric": "",
              "refId": "A",
              "step": 1
            }
          ],
          "title": "99th percentile write latency by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "si:reads/s"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 0,
            "y": 15
          },
          "id": 8,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "$func(rate(scylla_column_family_read_latency_count{ks=\"$ks\", cf=\"$table\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]])",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A",
              "step": 1
            }
          ],
          "title": "Reads by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "µs"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 6,
            "y": 15
          },
          "id": 9,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "rlatencyaks{ks=\"$ks\", cf=\"$table\", by=\"[[by]]\", instance=~\"[[node]]|$^\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A",
              "step": 1
            }
          ],
          "title": "Average read latency by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "µs"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 12,
            "y": 15
          },
          "id": 10,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "rlatencyp95ks{ks=\"$ks\", cf=\"$table\", by=\"[[by]]\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"} ",
              "intervalFactor": 1,
              "legendFormat": "",
              "metric": "",
              "refId": "A",
              "step": 1
            }
          ],
          "title": "95th percentile read latency by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "µs"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 18,
            "y": 15
          },
          "id": 11,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "rlatencyp99ks{ks=\"$ks\", cf=\"$table\", by=\"[[by]]\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"} ",
              "intervalFactor": 1,
              "legendFormat": "",
              "metric": "",
              "refId": "A",
              "step": 1
            }
          ],
          "title": "99th percentile read latency by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMax": 1,
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "percentunit"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 0,
            "y": 21
          },
          "id": 12,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "avg(scylla_column_family_cache_hit_rate{ks=\"$ks\", cf=\"$table\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}) by([[by]])",
              "intervalFactor": 1,
              "legendFormat": "",
              "metric": "",
              "refId": "A",
              "step": 1
            }
          ],
          "title": "Avg cache hit rate by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "bytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 6,
            "y": 21
          },
          "id": 13,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "$func(scylla_column_family_total_disk_space{ks=\"$ks\", cf=\"$table\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}) by([[by]])",
              "intervalFactor": 1,
              "legendFormat": "",
              "metric": "",
              "refId": "A",
              "step": 1
            }
          ],
          "title": "Total disk space by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "bytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 12,
            "y": 21
          },
          "id": 14,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "$func(scylla_column_family_live_disk_space{ks=\"$ks\", cf=\"$table\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}) by([[by]])",
              "intervalFactor": 1,
              "legendFormat": "",
              "metric": "",
              "refId": "A",
              "step": 1
            }
          ],
          "title": "Live disk space by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 18,
            "y": 21
          },
          "id": 15,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "$func(scylla_column_family_live_sstable{ks=\"$ks\", cf=\"$table\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}) by([[by]])",
              "intervalFactor": 1,
              "legendFormat": "",
              "metric": "",
              "refId": "A",
              "step": 1
            }
          ],
          "title": "Live sstables by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "axisSoftMin": 0,
                "barAlignment": 0,
                "barWidthFactor": 0.6,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "insertNulls": false,
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 0,
            "y": 27
          },
          "id": 16,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "tooltip": {
              "maxHeight": 600,
              "mode": "multi",
              "sort": "desc"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "$func(scylla_column_family_tablet_count{ks=\"$ks\", cf=\"$table\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}) by([[by]])",
              "intervalFactor": 1,
              "legendFormat": "",
              "metric": "",
              "range": true,
              "refId": "A",
              "step": 1
            }
          ],
          "title": "Tablets by [[by]]",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "custom": {
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "scaleDistribution": {
                  "type": "linear"
                }
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 13,
            "w": 24,
            "x": 0,
            "y": 33
          },
          "id": 17,
          "options": {
            "barRadius": 0,
            "barWidth": 0.89,
            "calculate": false,
            "cellGap": 1,
            "color": {
              "exponent": 0.5,
              "fill": "dark-orange",
              "min": 0,
              "mode": "scheme",
              "reverse": false,
              "scale": "exponential",
              "scheme": "Oranges",
              "steps": 64
            },
            "exemplars": {
              "color": "rgba(255,0,255,0.7)"
            },
            "filterValues": {
              "le": 1e-9
            },
            "fullHighlight": false,
            "groupWidth": 0.7,
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "show": true,
              "showLegend": false
            },
            "orientation": "auto",
            "rowsFrame": {
              "layout": "auto"
            },
            "showValue": "always",
            "stacking": "none",
            "tooltip": {
              "mode": "single",
              "showColorScale": false,
              "sort": "none",
              "yHistogram": false
            },
            "xTickLabelRotation": 0,
            "xTickLabelSpacing": 0,
            "yAxis": {
              "axisPlacement": "left",
              "reverse": false
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "expr": "sum(scylla_column_family_tablet_count{ks=\"$ks\", cf=\"$table\", instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$\"}) by([[by]])",
              "format": "time_series",
              "instant": false,
              "legendFormat": "{{dc}} {{instance}} {{shard}}",
              "range": false,
              "refId": "A"
            }
          ],
          "title": "Tablets over time per [[by]]",
          "type": "heatmap"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisBorderShow": false,
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "fillOpacity": 80,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineWidth": 1,
                "scaleDistribution": {
                  "type": "linear"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 24,
            "x": 0,
            "y": 46
          },
          "id": 18,
          "options": {
            "barRadius": 0,
            "barWidth": 0.89,
            "fullHighlight": false,
            "groupWidth": 0.7,
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": false
            },
            "orientation": "auto",
            "showValue": "always",
            "stacking": "none",
            "tooltip": {
              "mode": "single",
              "sort": "none"
            },
            "xTickLabelRotation": 0,
            "xTickLabelSpacing": 0
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "sum(scylla_column_family_tablet_count{ks=\"$ks\", cf=\"$table\",instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$\"}) by([[by]])",
              "format": "table",
              "instant": true,
              "legendFormat": "__auto",
              "range": false,
              "refId": "A"
            }
          ],
          "title": "Tablets per [[by]]",
          "type": "barchart"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 228
          },
          "id": 19,
          "panels": [],
          "title": "",
          "type": "row"
        }
      ],
      "preload": false,
      "refresh": "5m",
      "schemaVersion": 40,
      "tags": [
        "6.2.3"
      ],
      "templating": {
        "list": [
          {
            "current": {
              "text": "instance",
              "value": "instance"
            },
            "includeAll": false,
            "label": "by",
            "name": "by",
            "options": [
              {
                "selected": false,
                "text": "Cluster",
                "value": "cluster"
              },
              {
                "selected": false,
                "text": "DC",
                "value": "dc"
              },
              {
                "selected": true,
                "text": "Instance",
                "value": "instance"
              },
              {
                "selected": false,
                "text": "Shard",
                "value": "instance,shard"
              }
            ],
            "query": "Cluster : cluster,DC : dc, Instance : instance, Shard : instance\\,shard",
            "type": "custom"
          },
          {
            "current": {
              "text": "EGT_ScyllaDB_Cluster_DEV",
              "value": "EGT_ScyllaDB_Cluster_DEV"
            },
            "datasource": "prometheus",
            "definition": "",
            "includeAll": false,
            "label": "cluster",
            "name": "cluster",
            "options": [],
            "query": "label_values(scylla_reactor_utilization, cluster)",
            "refresh": 2,
            "regex": "",
            "sort": 1,
            "type": "query"
          },
          {
            "allValue": ".*",
            "current": {
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": "prometheus",
            "definition": "",
            "includeAll": true,
            "label": "dc",
            "multi": true,
            "name": "dc",
            "options": [],
            "query": "label_values(scylla_reactor_utilization{cluster=~\"$cluster\"}, dc)",
            "refresh": 2,
            "regex": "",
            "sort": 1,
            "type": "query"
          },
          {
            "allValue": ".*",
            "current": {
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": "prometheus",
            "definition": "",
            "includeAll": true,
            "label": "node",
            "multi": true,
            "name": "node",
            "options": [],
            "query": "label_values(scylla_reactor_utilization{cluster=\"$cluster\", dc=~\"$dc\"}, instance)",
            "refresh": 2,
            "regex": "",
            "sort": 1,
            "type": "query"
          },
          {
            "allValue": ".*",
            "current": {
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": "prometheus",
            "definition": "",
            "includeAll": true,
            "label": "shard",
            "multi": true,
            "name": "shard",
            "options": [],
            "query": "label_values(scylla_reactor_utilization,shard)",
            "refresh": 2,
            "regex": "",
            "sort": 3,
            "type": "query"
          },
          {
            "current": {
              "text": "egt_dev_datascience",
              "value": "egt_dev_datascience"
            },
            "datasource": "prometheus",
            "definition": "",
            "includeAll": false,
            "label": "ks",
            "name": "ks",
            "options": [],
            "query": "label_values(scylla_column_family_cache_hit_rate{cluster=\"$cluster\"},ks)",
            "refresh": 2,
            "regex": "",
            "sort": 3,
            "type": "query"
          },
          {
            "allValue": ".*",
            "current": {
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": "prometheus",
            "definition": "",
            "includeAll": true,
            "label": "table",
            "multi": true,
            "name": "table",
            "options": [],
            "query": "label_values(scylla_column_family_cache_hit_rate{cluster=\"$cluster\", ks=\"$ks\"},cf)",
            "refresh": 2,
            "regex": "",
            "sort": 3,
            "type": "query"
          },
          {
            "current": {
              "text": "sum",
              "value": "sum"
            },
            "includeAll": false,
            "label": "Function",
            "name": "func",
            "options": [
              {
                "selected": true,
                "text": "sum",
                "value": "sum"
              },
              {
                "selected": false,
                "text": "avg",
                "value": "avg"
              },
              {
                "selected": false,
                "text": "max",
                "value": "max"
              },
              {
                "selected": false,
                "text": "min",
                "value": "min"
              },
              {
                "selected": false,
                "text": "stddev",
                "value": "stddev"
              },
              {
                "selected": false,
                "text": "stdvar",
                "value": "stdvar"
              }
            ],
            "query": "sum,avg,max,min,stddev,stdvar",
            "type": "custom"
          },
          {
            "current": {
              "text": "6.2.3",
              "value": "6.2.3"
            },
            "hide": 2,
            "includeAll": false,
            "name": "scylla_version",
            "options": [
              {
                "selected": true,
                "text": "6.2.3",
                "value": "6.2.3"
              }
            ],
            "query": "6.2.3",
            "type": "custom"
          },
          {
            "current": {
              "text": "4.10.0",
              "value": "4.10.0"
            },
            "hide": 2,
            "includeAll": false,
            "name": "monitoring_version",
            "options": [
              {
                "selected": true,
                "text": "4.10.0",
                "value": "4.10.0"
              }
            ],
            "query": "4.10.0",
            "type": "custom"
          },
          {
            "current": {
              "text": "scylla_column_family_write_latency_count",
              "value": "scylla_column_family_write_latency_count"
            },
            "datasource": "prometheus",
            "definition": "scylla_column_family_write_latency_count{cluster=\"$cluster\"}",
            "hide": 2,
            "includeAll": false,
            "name": "no_ks",
            "options": [],
            "query": {
              "query": "scylla_column_family_write_latency_count{cluster=\"$cluster\"}",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "/^(scylla_column_family_write_latency_count)/",
            "type": "query"
          }
        ]
      },
      "time": {
        "from": "now-30m",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "utc",
      "title": "Scylla Keyspaces",
      "uid": "ks-6-2-12",
      "version": 3,
      "weekStart": ""
    }