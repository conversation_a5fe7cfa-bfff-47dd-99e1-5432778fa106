{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4788, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 29, "panels": [], "repeat": "instance", "title": "Barman instance", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "displayName": "", "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": []}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 10, "w": 4, "x": 0, "y": 1}, "id": 14, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "(node_filesystem_size_bytes{instance=~\"$host\",mountpoint=\"/\"} - node_filesystem_free_bytes{instance=~\"$host\",mountpoint=\"/\"}) / node_filesystem_size_bytes{instance=~\"$host\",mountpoint=\"/\"} * 100", "hide": true, "interval": "", "legendFormat": "", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "1 - (sum(node_filesystem_size_bytes{instance=~\"$host\"}) / sum(node_filesystem_free_bytes{instance=~\"$host\"}))", "hide": true, "interval": "", "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "(node_filesystem_size_bytes{instance=~\"$host\",mountpoint=\"/\"} - node_filesystem_free_bytes{instance=~\"$host\",mountpoint=\"/\"}) / node_filesystem_size_bytes{instance=~\"$host\",mountpoint=\"/\"} * 100", "hide": false, "interval": "", "legendFormat": "", "range": true, "refId": "C"}], "title": "Storage usage", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": []}, "unit": "decbytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "available disk space"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 3], "fill": "dash"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "available disk space"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 3], "fill": "dash"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 4, "y": 1}, "id": 16, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "node_filesystem_size_bytes{instance=~\"$host\",mountpoint=\"/\"} - node_filesystem_avail_bytes{instance=~\"$host\",mountpoint=\"/\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "$instance: available disk space", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "node_filesystem_size_bytes{instance=~\"$host\",mountpoint=\"/\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "$instance: available disk space", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "node_filesystem_avail_bytes{instance=~\"$host\",mountpoint=\"/\"}", "hide": false, "interval": "", "legendFormat": "$instance: available disk space", "range": true, "refId": "C"}], "title": "Backup partition (/)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "#AD0317", "value": 1}, {"color": "#AD0317"}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 10, "w": 4, "x": 16, "y": 1}, "id": 2, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "count(barman_up{instance=~\"$instance.*\"}) - sum(barman_up{instance=~\"$instance.*\"})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Barman errors", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 20, "y": 1}, "id": 30, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "count(barman_backups_total{instance=~\"$instance.*\"}) by (server)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{server}}", "range": true, "refId": "A"}], "title": "Number of servers", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 20, "y": 6}, "id": 31, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(barman_backups_total{instance=~\"$instance.*\"})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Total number of backups", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 11}, "id": 27, "panels": [], "repeat": "server", "title": "Server $server", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 120, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 48}, {"color": "#d44a3a"}]}, "unit": "h"}, "overrides": []}, "gridPos": {"h": 10, "w": 4, "x": 0, "y": 12}, "id": 8, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "hour(time()- barman_last_backup{instance=~\"$instance.*\", server=\"$server\"})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "$instance: {{ server }}", "refId": "A"}], "title": "Last backup age", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 48}, {"color": "#d44a3a"}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 4, "y": 12}, "id": 24, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "time() - barman_first_backup{instance=~\"$instance.*\", server=\"$server\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "First backup age", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "#F2495C", "value": 1}, {"color": "#F2495C"}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 8, "y": 12}, "id": 23, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "barman_backups_failed{instance=~\"$instance.*\", server=\"$server\"}", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Failed backups", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 1, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "h"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 12}, "id": 6, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "hour(time() - barman_last_backup{instance=~\"$instance.*\", server=\"$server\"})", "format": "time_series", "hide": false, "instant": false, "interval": "15m", "intervalFactor": 1, "legendFormat": "$instance: {{ server }}", "refId": "B"}], "title": "Time since last backup", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 48}, {"color": "#d44a3a"}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 4, "y": 17}, "id": 25, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "barman_last_backup_copy_time{instance=~\"$instance.*\", server=\"$server\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Last backup copy time", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#AD0317", "value": null}, {"color": "#37872D", "value": 4}, {"color": "#299c46"}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 8, "y": 17}, "id": 19, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "barman_backups_total{instance=~\"$instance.*\", server=\"$server\"} - barman_backups_failed{instance=~\"$instance.*\", server=\"$server\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Available backups", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 22}, "id": 20, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "barman_backup_size{instance=~\"$instance.*\", server=\"$server\", number=\"1\"}", "format": "time_series", "hide": false, "interval": "15m", "intervalFactor": 1, "legendFormat": "$instance: {{ server }} backup size", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "barman_backup_wal_size{instance=~\"$instance.*\", server=\"$server\", number=\"1\"}", "format": "time_series", "interval": "15m", "intervalFactor": 1, "legendFormat": "$instance: {{ server }} WAL size", "refId": "B"}], "title": "Last backup size", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "************:9780", "value": "************:9780"}, "datasource": {"type": "prometheus"}, "definition": "label_values(barman_up, instance)", "includeAll": false, "label": "Instance", "name": "instance", "options": [], "query": {"query": "label_values(barman_up, instance)", "refId": "Prometheus-instance-Variable-Query"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"allValue": ".*", "current": {"text": "egt", "value": "egt"}, "datasource": {"type": "prometheus"}, "definition": "label_values(barman_up, server)", "includeAll": false, "label": "Server", "name": "server", "options": [], "query": "label_values(barman_up, server)", "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "barman-exporter", "value": "barman-exporter"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(node_boot_time_seconds,job)", "includeAll": false, "label": "Job", "name": "Job", "options": [], "query": {"query": "label_values(node_boot_time_seconds,job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "***********:9100", "value": "***********:9100"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(node_time_seconds{job=\"barman-node-exporter-sc\"},instance)", "includeAll": false, "label": "Host", "name": "host", "options": [], "query": {"qryType": 1, "query": "label_values(node_time_seconds{job=\"barman-node-exporter-sc\"},instance)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-2d", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Barman", "uid": "Jb2lonIWka", "version": 1, "weekStart": ""}