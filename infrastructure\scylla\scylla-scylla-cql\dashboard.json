﻿{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": "-- <PERSON>ana --",
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      },
      {
        "class": "annotation_restart",
        "datasource": null,
        "enable": true,
        "expr": "resets(scylla_gossip_heart_beat{cluster=\"$cluster\"}[$__rate_interval])>0",
        "hide": false,
        "iconColor": "rgba(255, 96, 96, 1)",
        "limit": 100,
        "name": "node_restart",
        "showIn": 0,
        "tagKeys": "instance,dc,cluster",
        "tags": [],
        "titleFormat": "restart",
        "type": "tags"
      },
      {
        "class": "annotation_stall",
        "datasource": null,
        "enable": false,
        "expr": "changes(scylla_stall_detector_reported{cluster=\"$cluster\"}[$__rate_interval])>0",
        "hide": false,
        "iconColor": "rgba(255, 96, 96, 1)",
        "limit": 100,
        "name": "stall detector",
        "showIn": 0,
        "tagKeys": "dc,instance,shard",
        "tags": [],
        "titleFormat": "Stall found",
        "type": "tags"
      },
      {
        "class": "annotation_schema_changed",
        "datasource": null,
        "enable": false,
        "expr": "changes(scylla_database_schema_changed{cluster=\"$cluster\"}[$__rate_interval])>0",
        "hide": false,
        "iconColor": "rgba(255, 96, 96, 1)",
        "limit": 100,
        "name": "Schema Changed",
        "showIn": 0,
        "tagKeys": "instance,dc,cluster",
        "tags": [],
        "titleFormat": "schema changed",
        "type": "tags"
      }
    ]
  },
  "editable": true,
  "gnetId": null,
  "graphTooltip": 1,
  "iteration": 1728386615026,
  "links": [
    {
      "asDropdown": true,
      "icon": "external link",
      "includeVars": true,
      "keepTime": true,
      "tags": [],
      "type": "dashboards"
    }
  ],
  "panels": [
    {
      "class": "collapsible_row_panel",
      "collapsed": false,
      "datasource": null,
      "gridPos": {
        "h": 1,
        "w": 24,
        "x": 0,
        "y": 0
      },
      "id": 3,
      "panels": [],
      "repeat": "",
      "title": "CQL By User",
      "type": "row"
    },
    {
      "class": "text_panel",
      "datasource": null,
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "custom": {}
        },
        "overrides": []
      },
      "gridPos": {
        "h": 2,
        "w": 24,
        "x": 0,
        "y": 1
      },
      "id": 4,
      "isNew": true,
      "links": [],
      "mode": "html",
      "options": {
        "content": "<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">CQL By User - Coordinator</h1>",
        "mode": "html"
      },
      "pluginVersion": "7.4.2",
      "span": 12,
      "style": {},
      "title": "",
      "transparent": true,
      "type": "text"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Counts the total number of CQL INSERT requests with/without conditions.",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 0,
        "y": 3
      },
      "id": 5,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_inserts{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) - sum(rate(scylla_cql_inserts_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_inserts{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) - sum(rate(scylla_cql_inserts_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 1
        }
      ],
      "title": "CQL Insert",
      "type": "timeseries"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Number of CQL SELECT commands generated by the user",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 6,
        "y": 3
      },
      "id": 6,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) - sum(rate(scylla_cql_reads_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) - sum(rate(scylla_cql_reads_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 1
        }
      ],
      "title": "CQL Reads",
      "type": "timeseries"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Counts the total number of CQL DELETE requests with/without conditions.",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 12,
        "y": 3
      },
      "id": 7,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_deletes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])-sum(rate(scylla_cql_deletes_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_deletes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])-sum(rate(scylla_cql_deletes_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 1
        }
      ],
      "title": "CQL Deletes",
      "type": "timeseries"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Counts the total number of CQL UPDATE requests with/without conditions.",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 18,
        "y": 3
      },
      "id": 8,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_updates{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])-sum(rate(scylla_cql_updates_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_updates{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])-sum(rate(scylla_cql_updates_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 1
        }
      ],
      "title": "CQL Updates",
      "type": "timeseries"
    },
    {
      "class": "graph_panel",
      "datasource": null,
      "description": "amount of CQL connections currently established",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 0,
        "y": 9
      },
      "id": 9,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "pointradius": 1,
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(scylla_transport_current_connections{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(scylla_transport_current_connections{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 30
        }
      ],
      "title": "Client CQL connections by [[by]]",
      "type": "timeseries"
    },
    {
      "class": "graph_panel",
      "datasource": null,
      "description": "Rate of CQL connections created",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 6,
        "y": 9
      },
      "id": 10,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "pointradius": 1,
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_transport_cql_connections{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_transport_cql_connections{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 30
        }
      ],
      "title": "Client CQL new connections by [[by]]",
      "type": "timeseries"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Counts the number of SELECT statements with BYPASS CACHE option",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 12,
        "y": 9
      },
      "id": 11,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_select_bypass_caches{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_select_bypass_caches{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 2,
          "refId": "A"
        }
      ],
      "title": "BYPASS CACHE",
      "type": "timeseries"
    },
    {
      "class": "graph_panel",
      "datasource": null,
      "description": "CQL errors by type, only active errors are shown",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 18,
        "y": 9
      },
      "id": 12,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "pointradius": 1,
      "span": 3,
      "targets": [
        {
          "expr": "sum(rate(scylla_transport_cql_errors_total{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]],type) >0",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 30
        }
      ],
      "title": "CQL Errors [[by]]",
      "type": "timeseries"
    },
    {
      "class": "graph_panel",
      "datasource": null,
      "description": "Number of Logged CQL batches command, each batched command is counted once.\n\nIs used to make sure that multiple mutations across multiple partitions happen atomically, that is, all the included mutations will eventually succeed. However, there is a performance penalty imposed by atomicity guarantee.",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 0,
        "y": 15
      },
      "id": 13,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "pointradius": 1,
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_batches_pure_logged{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_batches_pure_logged{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 30
        }
      ],
      "title": "CQL Logged Batches by [[by]]",
      "type": "timeseries"
    },
    {
      "class": "graph_panel",
      "datasource": null,
      "description": "Number of CQL batches command, each batched command is counted once.\n\nIs generally used to group mutations for a single partition and do not suffer from the performance penalty imposed by logged batches, but there is no atomicity guarantee for multi-partition updates.",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 6,
        "y": 15
      },
      "id": 14,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "pointradius": 1,
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_batches_pure_unlogged{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_batches_pure_unlogged{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 30
        }
      ],
      "title": "CQL Unlogged Batches by [[by]]",
      "type": "timeseries"
    },
    {
      "class": "graph_panel",
      "datasource": null,
      "description": "Number of CQL command batched. Each batch would add the number of commands inside the batch",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 12,
        "y": 15
      },
      "id": 15,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "pointradius": 1,
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_statements_in_batches{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_statements_in_batches{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 30
        }
      ],
      "title": "CQL Command In Batches by [[by]]",
      "type": "timeseries"
    },
    {
      "class": "graph_panel",
      "datasource": null,
      "description": "Number of CQL row reads",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 18,
        "y": 15
      },
      "id": 16,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "pointradius": 1,
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_rows_read{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_rows_read{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 30
        }
      ],
      "title": "CQL Row Reads [[by]]",
      "type": "timeseries"
    },
    {
      "class": "graph_panel",
      "datasource": null,
      "description": "Number of reads using secondary indexes",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 0,
        "y": 21
      },
      "id": 17,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "pointradius": 1,
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_secondary_index_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_secondary_index_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 30
        }
      ],
      "title": "Secondary indexes Reads [[by]]",
      "type": "timeseries"
    },
    {
      "class": "text_panel",
      "datasource": null,
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "custom": {}
        },
        "overrides": []
      },
      "gridPos": {
        "h": 3,
        "w": 24,
        "x": 0,
        "y": 27
      },
      "id": 18,
      "isNew": true,
      "links": [],
      "mode": "html",
      "options": {
        "content": "<span style=\"color:#5780C1;font-size: 2rem;\">CQL System tables</span></br><span style=\"\">The following information is based on Scylla Plugin configuration, check the </span><a target=\"_blank\" herf=\"https://monitoring.docs.scylladb.com/stable/install/monitor-without-docker.html#using-scylla-plugin-with-grafana\" onclick=\"window.open('https://monitoring.docs.scylladb.com/stable/install/monitor-without-docker.html#using-scylla-plugin-with-grafana', '_blank')\">documentation</a><span> for more details on how to enable it.</span>",
        "mode": "html"
      },
      "pluginVersion": "7.4.2",
      "span": 12,
      "style": {},
      "title": "",
      "transparent": true,
      "type": "text"
    },
    {
      "class": "collapsible_row_panel",
      "collapsed": false,
      "datasource": null,
      "gridPos": {
        "h": 1,
        "w": 24,
        "x": 0,
        "y": 30
      },
      "id": 27,
      "panels": [],
      "repeat": "",
      "title": "CQL Internal",
      "type": "row"
    },
    {
      "class": "text_panel",
      "datasource": null,
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "custom": {}
        },
        "overrides": []
      },
      "gridPos": {
        "h": 2,
        "w": 24,
        "x": 0,
        "y": 31
      },
      "id": 28,
      "isNew": true,
      "links": [],
      "mode": "html",
      "options": {
        "content": "<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">CQL Internal - Coordinator</h1>",
        "mode": "html"
      },
      "pluginVersion": "7.4.2",
      "span": 12,
      "style": {},
      "title": "",
      "transparent": true,
      "type": "text"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Number of CQL INSERT commands generated by internal operations",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 0,
        "y": 33
      },
      "id": 29,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_inserts_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_inserts_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 1
        }
      ],
      "title": "CQL Internal Insert",
      "type": "timeseries"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Number of CQL SELECT commands generated by internal operations",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 6,
        "y": 33
      },
      "id": 30,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_reads_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_reads_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 1
        }
      ],
      "title": "CQL Internal Reads",
      "type": "timeseries"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Counts the total number of CQL DELETE requests with/without conditions.",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 12,
        "y": 33
      },
      "id": 31,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_deletes_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_deletes_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 1
        }
      ],
      "title": "CQL Deletes",
      "type": "timeseries"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Counts the total number of CQL UPDATE requests with/without conditions.",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 18,
        "y": 33
      },
      "id": 32,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_updates_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_updates_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 1
        }
      ],
      "title": "CQL Updates",
      "type": "timeseries"
    },
    {
      "class": "collapsible_row_panel",
      "collapsed": false,
      "datasource": null,
      "gridPos": {
        "h": 1,
        "w": 24,
        "x": 0,
        "y": 39
      },
      "id": 33,
      "panels": [],
      "repeat": "",
      "title": "LWT",
      "type": "row"
    },
    {
      "class": "text_panel",
      "datasource": null,
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "custom": {}
        },
        "overrides": []
      },
      "gridPos": {
        "h": 2,
        "w": 24,
        "x": 0,
        "y": 40
      },
      "id": 34,
      "isNew": true,
      "links": [],
      "mode": "html",
      "options": {
        "content": "<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">LWT</h1>",
        "mode": "html"
      },
      "pluginVersion": "7.4.2",
      "span": 12,
      "style": {},
      "title": "",
      "transparent": true,
      "type": "text"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Counts the total number of CQL INSERT requests with/without conditions.",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 0,
        "y": 42
      },
      "id": 35,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_inserts{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", conditional=\"yes\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_inserts{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", conditional=\"yes\"}[$__rate_interval])) by ([[by]]))",
          "interval": "",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 1
        }
      ],
      "title": "CQL Insert",
      "type": "timeseries"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Counts the total number of CQL DELETE requests with/without conditions.",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 6,
        "y": 42
      },
      "id": 36,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_deletes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", conditional=\"yes\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_deletes{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", conditional=\"yes\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 1
        }
      ],
      "title": "CQL Deletes",
      "type": "timeseries"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Counts the total number of CQL UPDATE requests with/without conditions.",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 12,
        "y": 42
      },
      "id": 37,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_updates{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", conditional=\"yes\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_updates{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", conditional=\"yes\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 1
        }
      ],
      "title": "CQL Updates",
      "type": "timeseries"
    },
    {
      "class": "graph_panel",
      "datasource": null,
      "description": "Number of CQL batches command, each batched command is counted once",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 18,
        "y": 42
      },
      "id": 38,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "pointradius": 1,
      "span": 3,
      "targets": [
        {
          "expr": "topk([[topk]], sum(rate(scylla_cql_batches{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", conditional=\"yes\"}[$__rate_interval])) by ([[by]])) or on ([[by]]) bottomk([[bottomk]], sum(rate(scylla_cql_batches{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", conditional=\"yes\"}[$__rate_interval])) by ([[by]]))",
          "intervalFactor": 1,
          "legendFormat": "",
          "metric": "",
          "refId": "A",
          "step": 30
        }
      ],
      "title": "CQL Batches by [[by]]",
      "type": "timeseries"
    },
    {
      "class": "collapsible_row_panel",
      "collapsed": false,
      "datasource": null,
      "gridPos": {
        "h": 1,
        "w": 24,
        "x": 0,
        "y": 48
      },
      "id": 39,
      "panels": [],
      "repeat": "",
      "title": "Optimization",
      "type": "row"
    },
    {
      "class": "text_panel",
      "datasource": null,
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "custom": {}
        },
        "overrides": []
      },
      "gridPos": {
        "h": 2,
        "w": 24,
        "x": 0,
        "y": 49
      },
      "id": 40,
      "isNew": true,
      "links": [],
      "mode": "html",
      "options": {
        "content": "<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">Optimization</h1>",
        "mode": "html"
      },
      "pluginVersion": "7.4.2",
      "span": 12,
      "style": {},
      "title": "",
      "transparent": true,
      "type": "text"
    },
    {
      "cacheTimeout": null,
      "class": "gauge_errors_panel",
      "datasource": null,
      "description": "All of the requests should be prepared\n\nPrepared statements remove the overhead of parsing the query every time and allow optimal routing of requests from client to server",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {},
          "mappings": [
            {
              "id": 0,
              "op": "=",
              "text": "N/A",
              "type": 1,
              "value": "null"
            }
          ],
          "max": 100,
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "#299c46",
                "value": null
              },
              {
                "color": "rgba(237, 129, 40, 0.89)",
                "value": 4
              },
              {
                "color": "#d44a3a",
                "value": 10
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 2,
        "x": 0,
        "y": 51
      },
      "id": 66,
      "links": [],
      "options": {
        "orientation": "horizontal",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true,
        "text": {}
      },
      "pluginVersion": "7.4.2",
      "span": 1,
      "targets": [
        {
          "expr": "floor(100 *sum(cql:non_system_prepared1m)/ (sum(cql:all_shardrate1m{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) - sum(cql:all_system_shardrate1m{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}))) OR vector(0)",
          "format": "time_series",
          "hide": false,
          "instant": false,
          "interval": "",
          "intervalFactor": 1,
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "CQL Non-Prepared Statements",
      "transparent": true,
      "type": "gauge"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "All of the requests should be prepared\n",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 2,
        "y": 51
      },
      "id": 67,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "sum(cql:non_system_prepared1m{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])",
          "format": "time_series",
          "hide": false,
          "interval": "",
          "intervalFactor": 2,
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "CQL Non-Prepared Statements",
      "type": "timeseries"
    },
    {
      "cacheTimeout": null,
      "class": "gauge_errors_panel",
      "datasource": null,
      "description": "All requests should be paged\n\nNon Paged request sources:\n- Client modifying the fetch size\n\nNon Paged requests require reading all the results and returning them in a single request.",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {},
          "mappings": [
            {
              "id": 0,
              "op": "=",
              "text": "N/A",
              "type": 1,
              "value": "null"
            }
          ],
          "max": 100,
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "#299c46",
                "value": null
              },
              {
                "color": "rgba(237, 129, 40, 0.89)",
                "value": 4
              },
              {
                "color": "#d44a3a",
                "value": 10
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 2,
        "x": 8,
        "y": 51
      },
      "id": 43,
      "links": [],
      "options": {
        "orientation": "horizontal",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true,
        "text": {}
      },
      "pluginVersion": "7.4.2",
      "span": 1,
      "targets": [
        {
          "expr": "100 * ((sum(rate(scylla_cql_unpaged_select_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]))-sum(rate(scylla_cql_unpaged_select_queries_per_ks{ks=\"system\",instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])))/sum(rate(scylla_cql_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]))) OR vector(0)",
          "format": "time_series",
          "hide": false,
          "instant": false,
          "interval": "",
          "intervalFactor": 1,
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "Non-Paged CQL Reads",
      "transparent": true,
      "type": "gauge"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Non-Paged requests require reading all the results and returning them in a single request",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 10,
        "y": 51
      },
      "id": 44,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "sum(rate(scylla_cql_unpaged_select_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])-sum(rate(scylla_cql_unpaged_select_queries_per_ks{ks=\"system\",instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 2,
          "refId": "A"
        }
      ],
      "title": "Non-Paged CQL Reads",
      "type": "timeseries"
    },
    {
      "cacheTimeout": null,
      "class": "gauge_errors_panel",
      "datasource": null,
      "description": "All of the requests should be Token Aware\n\nNon Token Aware requests sources:\n* Non-Prepared Stamements\n* Client not using a Token Aware load balancing policy\n\nTokenAware requests are sent to a Scylla node that is also a replica. Token Un-Aware requests require extra hop and additional processing.\n\nNote that the metric shows incorrect values when batches are used.",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {},
          "mappings": [
            {
              "id": 0,
              "op": "=",
              "text": "N/A",
              "type": 1,
              "value": "null"
            }
          ],
          "max": 100,
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "#299c46",
                "value": null
              },
              {
                "color": "rgba(237, 129, 40, 0.89)",
                "value": 4
              },
              {
                "color": "#d44a3a",
                "value": 10
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 2,
        "x": 16,
        "y": 51
      },
      "id": 45,
      "links": [],
      "options": {
        "orientation": "horizontal",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true,
        "text": {}
      },
      "pluginVersion": "7.4.2",
      "span": 1,
      "targets": [
        {
          "expr": "clamp_max(100*((sum(rate(scylla_storage_proxy_coordinator_reads_coordinator_outside_replica_set{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) or on() vector(0)) + (sum(rate(scylla_storage_proxy_coordinator_writes_coordinator_outside_replica_set{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) or on() vector(0)))/((sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) + 1) or vector(1)),100) OR vector(0)",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 1,
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "Non-Token Aware",
      "transparent": true,
      "type": "gauge"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Requests that are not token aware indicates that requests are not routed to the right node, which require extra hop and additional processing.\n\nNote that the metric shows incorrect values when batches are used.",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 18,
        "y": 51
      },
      "id": 46,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "(sum(rate(scylla_storage_proxy_coordinator_reads_coordinator_outside_replica_set{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() sum(scylla_reactor_utilization*0) by([[by]])) + (sum(rate(scylla_storage_proxy_coordinator_writes_coordinator_outside_replica_set{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on() sum(scylla_reactor_utilization*0) by([[by]]))",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 2,
          "refId": "A"
        }
      ],
      "title": "Non-Token Aware Queries",
      "type": "timeseries"
    },
    {
      "cacheTimeout": null,
      "class": "gauge_errors_panel",
      "datasource": null,
      "description": "Reversed CQL Reads entail additional processing on server side\n\nSources: CQL Read requests with ORDER BY that is different from the \"CLUSTERING ORDER BY\" of the table\nAlternatives:\n\n* Denormalize your data (use a Materialized View)",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {},
          "mappings": [
            {
              "id": 0,
              "op": "=",
              "text": "N/A",
              "type": 1,
              "value": "null"
            }
          ],
          "max": 100,
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "#299c46",
                "value": null
              },
              {
                "color": "rgba(237, 129, 40, 0.89)",
                "value": 4
              },
              {
                "color": "#d44a3a",
                "value": 10
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 2,
        "x": 0,
        "y": 57
      },
      "id": 47,
      "links": [],
      "options": {
        "orientation": "horizontal",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true,
        "text": {}
      },
      "pluginVersion": "7.4.2",
      "span": 1,
      "targets": [
        {
          "expr": "100 * sum(rate(scylla_cql_reverse_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) / sum(rate(scylla_cql_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) OR vector(0)",
          "format": "time_series",
          "hide": false,
          "instant": false,
          "interval": "",
          "intervalFactor": 1,
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "Reversed CQL Reads",
      "transparent": true,
      "type": "gauge"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Reversed CQL Reads entail additional processing on server side and should be avoided",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 2,
        "y": 57
      },
      "id": 48,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "sum(rate(scylla_cql_reverse_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 2,
          "refId": "A"
        }
      ],
      "title": "Reversed CQL Reads",
      "type": "timeseries"
    },
    {
      "cacheTimeout": null,
      "class": "gauge_errors_panel",
      "datasource": null,
      "description": "ALLOW FILTERING CQL Reads, the percentage of read requests with 'ALLOW FILTERING'\n\nALLOW FILTERING CQL Reads entail additional processing on server side\n\nSources: CQL Read requests with \"ALLOW FILTERING\"\n\nALLOW FILTERING should be used when large parts of the filtered data is returned - check \n\"ALLOW FILTERING CQL Read Filtered Rows to check what percentage of the data is used\"\n\nAlternatives:\n- Use a Secondary Index\n- Denormalize your data (use a Materialized View)",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {},
          "mappings": [
            {
              "id": 0,
              "op": "=",
              "text": "N/A",
              "type": 1,
              "value": "null"
            }
          ],
          "max": 100,
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "#299c46",
                "value": null
              },
              {
                "color": "rgba(237, 129, 40, 0.89)",
                "value": 4
              },
              {
                "color": "#d44a3a",
                "value": 10
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 2,
        "x": 8,
        "y": 57
      },
      "id": 49,
      "links": [],
      "options": {
        "orientation": "horizontal",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true,
        "text": {}
      },
      "pluginVersion": "7.4.2",
      "span": 1,
      "targets": [
        {
          "expr": "100 * sum(rate(scylla_cql_filtered_read_requests{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) / sum(rate(scylla_cql_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) OR vector(0)",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 1,
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "ALLOW FILTERING CQL Reads",
      "transparent": true,
      "type": "gauge"
    },
    {
      "class": "rps_panel",
      "datasource": null,
      "description": "Read requests with ALLOW FILTERING\n\nALLOW FILTERING CQL Reads entail additional processing on server side and should be avoided",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:reads/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 10,
        "y": 57
      },
      "id": 50,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "span": 3,
      "targets": [
        {
          "expr": "sum(rate(scylla_cql_filtered_read_requests{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 2,
          "refId": "A"
        }
      ],
      "title": "ALLOW FILTERING CQL Reads",
      "type": "timeseries"
    },
    {
      "cacheTimeout": null,
      "class": "gauge_errors_panel",
      "datasource": null,
      "description": "ALLOW FILTERING Filtered rows, the percentage of rows that were read and then filtered.\n\nALLOW FILTERING CQL Reads entail additional processing on server side. \nReading a row and then filter it is a waste of resources.\n\nSources: CQL Read requests with \"ALLOW FILTERING\"\n\nALLOW FILTERING should be used when large parts of the filtered data is returned\n\nAlternatives:\n- Use a Secondary Index\n- Denormalize your data (use a Materialized View)",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {},
          "mappings": [
            {
              "id": 0,
              "op": "=",
              "text": "N/A",
              "type": 1,
              "value": "null"
            }
          ],
          "max": 100,
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "#299c46",
                "value": null
              },
              {
                "color": "rgba(237, 129, 40, 0.89)",
                "value": 4
              },
              {
                "color": "#d44a3a",
                "value": 10
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 2,
        "x": 16,
        "y": 57
      },
      "id": 51,
      "links": [],
      "options": {
        "orientation": "horizontal",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true,
        "text": {}
      },
      "pluginVersion": "7.4.2",
      "span": 1,
      "targets": [
        {
          "expr": "100 * sum(rate(scylla_cql_filtered_rows_dropped_total{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]))  /sum(rate(scylla_cql_filtered_rows_read_total{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) OR vector(0)",
          "format": "time_series",
          "hide": false,
          "instant": false,
          "interval": "",
          "intervalFactor": 1,
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "ALLOW FILTERING Filtered Rows",
      "transparent": true,
      "type": "gauge"
    },
    {
      "class": "rps_panel",
      "datasource": null,
      "description": "CQL Queries with ALLOW FILTERING should be avoided.\nDropped rows are rows that were read but were filtered by the server.\nWhen dropped rows is relatively high you should consider the alternatives",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:reads/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 18,
        "y": 57
      },
      "id": 52,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "span": 3,
      "targets": [
        {
          "expr": "sum(rate(scylla_cql_filtered_rows_read_total{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 2,
          "legendFormat": "rows read $node $shard",
          "refId": "A"
        },
        {
          "expr": "sum(rate(scylla_cql_filtered_rows_matched_total{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])",
          "format": "time_series",
          "hide": false,
          "interval": "",
          "intervalFactor": 2,
          "legendFormat": "rows matched $node $shard",
          "refId": "B"
        },
        {
          "expr": "sum(rate(scylla_cql_filtered_rows_dropped_total{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])",
          "format": "time_series",
          "intervalFactor": 2,
          "legendFormat": "rows dropped $node $shard",
          "refId": "C"
        }
      ],
      "title": "ALLOW FILTERING CQL Read Filtering",
      "type": "timeseries"
    },
    {
      "cacheTimeout": null,
      "class": "gauge_errors_panel",
      "datasource": null,
      "description": "Range scans should typically by pass the cache.\n\n Add BYPASS CACHE to your select queries.",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {},
          "mappings": [
            {
              "id": 0,
              "op": "=",
              "text": "N/A",
              "type": 1,
              "value": "null"
            }
          ],
          "max": 100,
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "#299c46",
                "value": null
              },
              {
                "color": "rgba(237, 129, 40, 0.89)",
                "value": 4
              },
              {
                "color": "#d44a3a",
                "value": 10
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 2,
        "x": 0,
        "y": 63
      },
      "id": 53,
      "links": [],
      "options": {
        "orientation": "horizontal",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true,
        "text": {}
      },
      "pluginVersion": "7.4.2",
      "span": 1,
      "targets": [
        {
          "expr": "floor(100 *sum(rate(scylla_cql_select_partition_range_scan_no_bypass_cache{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]))/(sum(rate(scylla_cql_reads{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) - sum(rate(scylla_cql_reads_per_ks{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", who=\"internal\"}[$__rate_interval])) )) OR vector(0)",
          "format": "time_series",
          "hide": false,
          "instant": false,
          "interval": "",
          "intervalFactor": 1,
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "Range Scans",
      "transparent": true,
      "type": "gauge"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Range scans should typically by pass the cache.\n\n Add BYPASS CACHE to your select queries.",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 2,
        "y": 63
      },
      "id": 54,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "sum(rate(scylla_cql_select_partition_range_scan_no_bypass_cache{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 2,
          "refId": "A"
        }
      ],
      "title": "Range Scans Without BYPASS CACHE ",
      "type": "timeseries"
    },
    {
      "cacheTimeout": null,
      "class": "gauge_errors_panel",
      "datasource": null,
      "description": "Using consistency level ANY in a query may hurt persistency, if the node receiving the request will fail the data may be lost",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {},
          "mappings": [
            {
              "id": 0,
              "op": "=",
              "text": "N/A",
              "type": 1,
              "value": "null"
            }
          ],
          "max": 100,
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "#299c46",
                "value": null
              },
              {
                "color": "rgba(237, 129, 40, 0.89)",
                "value": 4
              },
              {
                "color": "#d44a3a",
                "value": 10
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 2,
        "x": 8,
        "y": 63
      },
      "id": 55,
      "links": [],
      "options": {
        "orientation": "horizontal",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true,
        "text": {}
      },
      "pluginVersion": "7.4.2",
      "span": 1,
      "targets": [
        {
          "expr": "floor(100 *sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", consistency_level=\"ANY\"}[$__rate_interval]))/sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]))) OR vector(0)",
          "format": "time_series",
          "hide": false,
          "instant": false,
          "interval": "",
          "intervalFactor": 1,
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "CQL ANY Queries",
      "transparent": true,
      "type": "gauge"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Using consistency level ANY in a query may hurt persistency, if the node receiving the request will fail the data may be lost",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 10,
        "y": 63
      },
      "id": 56,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",  consistency_level=\"ANY\"}[$__rate_interval])) by ([[by]])",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 2,
          "refId": "A"
        }
      ],
      "title": "CQL ANY CL Queries",
      "type": "timeseries"
    },
    {
      "cacheTimeout": null,
      "class": "gauge_errors_panel",
      "datasource": null,
      "description": "Using consistency level ALL in a query may hurt availability, if a node is unavailable operations will fail",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {},
          "mappings": [
            {
              "id": 0,
              "op": "=",
              "text": "N/A",
              "type": 1,
              "value": "null"
            }
          ],
          "max": 100,
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "#299c46",
                "value": null
              },
              {
                "color": "rgba(237, 129, 40, 0.89)",
                "value": 4
              },
              {
                "color": "#d44a3a",
                "value": 10
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 2,
        "x": 16,
        "y": 63
      },
      "id": 57,
      "links": [],
      "options": {
        "orientation": "horizontal",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true,
        "text": {}
      },
      "pluginVersion": "7.4.2",
      "span": 1,
      "targets": [
        {
          "expr": "floor(100 *sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", consistency_level=\"ALL\"}[$__rate_interval]))/sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]))) OR vector(0)",
          "format": "time_series",
          "hide": false,
          "instant": false,
          "interval": "",
          "intervalFactor": 1,
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "CQL ALL CL Queries",
      "transparent": true,
      "type": "gauge"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "Using consistency level ALL in a query may hurt availability, if a node is unavailable operations will fail",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 18,
        "y": 63
      },
      "id": 58,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",  consistency_level=\"ALL\"}[$__rate_interval])) by ([[by]])",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 2,
          "refId": "A"
        }
      ],
      "title": "CQL ALL CL Queries",
      "type": "timeseries"
    },
    {
      "class": "text_panel",
      "datasource": null,
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "custom": {}
        },
        "overrides": []
      },
      "gridPos": {
        "h": 3,
        "w": 24,
        "x": 0,
        "y": 69
      },
      "id": 59,
      "isNew": true,
      "links": [],
      "mode": "html",
      "options": {
        "content": "<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">Cross DC Information</h1><h5>This section is relevant only if you have more than one DC</h5>",
        "mode": "html"
      },
      "pluginVersion": "7.4.2",
      "span": 12,
      "style": {},
      "title": "",
      "transparent": true,
      "type": "text"
    },
    {
      "cacheTimeout": null,
      "class": "gauge_errors_panel",
      "datasource": null,
      "description": "How many Queries use Consistency level ONE\n\nThis is an issue when using multiple datacenters.\n\nUsing consistency level ONE in a query when there is more than one DC may hurt performance, queries may end in the non-local DC. Use LOCAL_ONE instead",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {},
          "mappings": [
            {
              "id": 0,
              "op": "=",
              "text": "N/A",
              "type": 1,
              "value": "null"
            }
          ],
          "max": 100,
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "#299c46",
                "value": null
              },
              {
                "color": "rgba(237, 129, 40, 0.89)",
                "value": 4
              },
              {
                "color": "#d44a3a",
                "value": 10
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 2,
        "x": 0,
        "y": 72
      },
      "id": 60,
      "links": [],
      "options": {
        "orientation": "horizontal",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true,
        "text": {}
      },
      "pluginVersion": "7.4.2",
      "span": 1,
      "targets": [
        {
          "expr": "floor(100 *sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", consistency_level=\"ONE\"}[$__rate_interval]))/sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]))) OR vector(0)",
          "format": "time_series",
          "hide": false,
          "instant": false,
          "interval": "",
          "intervalFactor": 1,
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "CQL ONE Queries",
      "transparent": true,
      "type": "gauge"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "How many Queries use Consistency level ONE\n\nThis is an issue when using multiple datacenters.\n\nUsing consistency level ONE in a query when there is more than one DC may hurt performance, queries may end in the non-local DC. Use LOCAL_ONE instead",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 2,
        "y": 72
      },
      "id": 61,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",  consistency_level=\"ONE\"}[$__rate_interval])) by ([[by]])",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 2,
          "refId": "A"
        }
      ],
      "title": "CQL ONE CL Queries",
      "type": "timeseries"
    },
    {
      "cacheTimeout": null,
      "class": "gauge_errors_panel",
      "datasource": null,
      "description": "How many Queries use Consistency level QUORUM\n\nThis is an issue when using multiple datacenters.\n\nUsing consistency level QUORUM in a query when there is more than one DC may hurt performance, queries may end in the non-local DC. Use LOCAL_QUORUM instead",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {},
          "mappings": [
            {
              "id": 0,
              "op": "=",
              "text": "N/A",
              "type": 1,
              "value": "null"
            }
          ],
          "max": 100,
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "#299c46",
                "value": null
              },
              {
                "color": "rgba(237, 129, 40, 0.89)",
                "value": 4
              },
              {
                "color": "#d44a3a",
                "value": 10
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 2,
        "x": 8,
        "y": 72
      },
      "id": 62,
      "links": [],
      "options": {
        "orientation": "horizontal",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true,
        "text": {}
      },
      "pluginVersion": "7.4.2",
      "span": 1,
      "targets": [
        {
          "expr": "floor(100 *sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\", consistency_level=\"QUORUM\"}[$__rate_interval]))/sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]))) OR vector(0)",
          "format": "time_series",
          "hide": false,
          "instant": false,
          "interval": "",
          "intervalFactor": 1,
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "CQL QUORUM CL Queries",
      "transparent": true,
      "type": "gauge"
    },
    {
      "class": "ops_panel",
      "datasource": null,
      "description": "How many Queries use Consistency level QUORUM\n\nThis is an issue when using multiple datacenters.\n\nUsing consistency level QUORUM in a query when there is more than one DC may hurt performance, queries may end in the non-local DC. Use LOCAL_QUORUM instead",
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "axisSoftMin": 0,
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "graph": false,
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {},
            "thresholdsStyle": {}
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "si:ops/s"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 6,
        "x": 10,
        "y": 72
      },
      "id": 63,
      "isNew": true,
      "links": [],
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "maxHeight": 600,
          "mode": "multi",
          "sort": "desc"
        },
        "tooltipOptions": {
          "mode": "single"
        }
      },
      "seriesOverrides": [
        {}
      ],
      "span": 3,
      "targets": [
        {
          "expr": "sum(rate(scylla_query_processor_queries{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",  consistency_level=\"QUORUM\"}[$__rate_interval])) by ([[by]])",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 2,
          "refId": "A"
        }
      ],
      "title": "CQL QUORUM CL Queries",
      "type": "timeseries"
    },
    {
      "cacheTimeout": null,
      "class": "gauge_errors_panel",
      "datasource": null,
      "description": "Cross DC traffic may cause additional latencies and network loads and in most cases, should be avoided.\n\nCross DC Read requests sources:\n- Consistency Level that is not LOCAL_XXX\n- Tables with read_repair_chance > 0\n\nNote:\n- If requests are supposed to be  DC local - verify client is using a DCAware policy and a LOCAL_XX consistency level",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {},
          "mappings": [
            {
              "id": 0,
              "op": "=",
              "text": "N/A",
              "type": 1,
              "value": "null"
            }
          ],
          "max": 100,
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "#299c46",
                "value": null
              },
              {
                "color": "rgba(237, 129, 40, 0.89)",
                "value": 4
              },
              {
                "color": "#d44a3a",
                "value": 10
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 6,
        "w": 24,
        "x": 0,
        "y": 78
      },
      "id": 64,
      "links": [],
      "options": {
        "orientation": "horizontal",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true,
        "text": {}
      },
      "pluginVersion": "7.4.2",
      "repeat": "dc",
      "scopedVars": {
        "dc": {
          "selected": true,
          "text": "datacenter1",
          "value": "datacenter1"
        }
      },
      "span": 2,
      "targets": [
        {
          "expr": "100*(sum(rate(scylla_storage_proxy_coordinator_reads_remote_node{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) - sum(rate(scylla_storage_proxy_coordinator_reads_remote_node{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", datacenter=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])))/sum(rate(scylla_storage_proxy_coordinator_reads_remote_node{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) OR vector(0)",
          "format": "time_series",
          "intervalFactor": 1,
          "refId": "A"
        }
      ],
      "title": "Cross DC read requests $dc",
      "transparent": true,
      "type": "gauge"
    },
    {
      "class": "plain_text",
      "datasource": null,
      "editable": true,
      "error": false,
      "fieldConfig": {
        "defaults": {
          "custom": {}
        },
        "overrides": []
      },
      "gridPos": {
        "h": 3,
        "w": 24,
        "x": 0,
        "y": 84
      },
      "id": 65,
      "isNew": true,
      "links": [],
      "mode": "html",
      "options": {
        "content": "<div style=\"color:#5780C1; border-bottom: 0px solid #5780C1;\">Scylla Monitoring version - master</div> <div style=\"\"><a href=\"https://github.com/scylladb/scylla-monitoring/issues/new?body=scylla-version%3D[[scylla_version]]%0Amonitoring-version%3D[[monitoring_version]]%0Adashboard%3D${__dashboard.uid}\" target=\"_blank\">\n<input title=\"Report an issue with Scylla Monitoring\" type=\"button\" style=\"background-color:#306EE6;color:white;border-radius: 15px;font-size:14px;line-height:22px;padding:2px\" value=\"&nbsp;Report an issue on this page&nbsp;\"></a>&nbsp;&nbsp;&nbsp;&nbsp;<a href=\"/render/d/${__dashboard.uid}?orgId=1&from=${__from}&to=${__to}&width=1600&height=-1&kiosk\" target=\"_blank\" download=\"dashboard_${__dashboard.uid}-${__from:date:iso}.png\"><input title=\"Make a Screenshot\" type=\"button\" style=\"background-color:#306EE6;color:white;border-radius: 15px;font-size:14px;line-height:22px;padding:2px\" value=\"&nbsp;&nbsp;&nbsp;Screenshot&nbsp;&nbsp;&nbsp;\"></input></a></span></div>",
        "mode": "html"
      },
      "pluginVersion": "7.4.2",
      "span": 12,
      "style": {},
      "title": "",
      "transparent": true,
      "type": "text"
    }
  ],
  "refresh": "30s",
  "schemaVersion": 27,
  "style": "dark",
  "tags": [
    "6.0.1"
  ],
  "templating": {
    "list": [
      {
        "allValue": null,
        "class": "by_template_var",
        "current": {
          "selected": true,
          "text": "Instance",
          "value": "instance"
        },
        "description": null,
        "error": null,
        "hide": 0,
        "includeAll": false,
        "label": "by",
        "multi": false,
        "name": "by",
        "options": [
          {
            "selected": false,
            "text": "Cluster",
            "value": "cluster"
          },
          {
            "selected": false,
            "text": "DC",
            "value": "dc"
          },
          {
            "selected": true,
            "text": "Instance",
            "value": "instance"
          },
          {
            "selected": false,
            "text": "Shard",
            "value": "instance,shard"
          }
        ],
        "query": "Cluster : cluster,DC : dc, Instance : instance, Shard : instance\\,shard",
        "queryValue": "",
        "skipUrlSync": false,
        "type": "custom"
      },
      {
        "allValue": null,
        "class": "template_variable_single",
        "current": {
          "selected": false,
          "text": "EGT_ScyllaDB_Cluster_DEV",
          "value": "EGT_ScyllaDB_Cluster_DEV"
        },
        "datasource": null,
        "definition": "label_values(scylla_reactor_utilization, cluster)",
        "description": null,
        "error": null,
        "hide": 0,
        "includeAll": false,
        "label": "cluster",
        "multi": false,
        "name": "cluster",
        "options": [],
        "query": {
          "query": "label_values(scylla_reactor_utilization, cluster)",
          "refId": "StandardVariableQuery"
        },
        "refresh": 2,
        "regex": "",
        "skipUrlSync": false,
        "sort": 1,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "allValue": ".*",
        "class": "template_variable_all",
        "current": {
          "selected": true,
          "text": [
            "datacenter1"
          ],
          "value": [
            "datacenter1"
          ]
        },
        "datasource": null,
        "definition": "label_values(scylla_reactor_utilization{cluster=~\"$cluster\"}, dc)",
        "description": null,
        "error": null,
        "hide": 0,
        "includeAll": true,
        "label": "dc",
        "multi": true,
        "name": "dc",
        "options": [],
        "query": {
          "query": "label_values(scylla_reactor_utilization{cluster=~\"$cluster\"}, dc)",
          "refId": "StandardVariableQuery"
        },
        "refresh": 2,
        "regex": "",
        "skipUrlSync": false,
        "sort": 1,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "allValue": ".*",
        "class": "template_variable_single",
        "current": {
          "selected": true,
          "text": [
            "All"
          ],
          "value": [
            "$__all"
          ]
        },
        "datasource": null,
        "definition": "label_values(scylla_reactor_utilization{cluster=\"$cluster\", dc=~\"$dc\"}, instance)",
        "description": null,
        "error": null,
        "hide": 0,
        "includeAll": true,
        "label": "node",
        "multi": true,
        "name": "node",
        "options": [],
        "query": {
          "query": "label_values(scylla_reactor_utilization{cluster=\"$cluster\", dc=~\"$dc\"}, instance)",
          "refId": "StandardVariableQuery"
        },
        "refresh": 2,
        "regex": "",
        "skipUrlSync": false,
        "sort": 1,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "allValue": ".*",
        "class": "template_variable_all",
        "current": {
          "selected": true,
          "text": [
            "All"
          ],
          "value": [
            "$__all"
          ]
        },
        "datasource": null,
        "definition": "label_values(scylla_reactor_utilization,shard)",
        "description": null,
        "error": null,
        "hide": 0,
        "includeAll": true,
        "label": "shard",
        "multi": true,
        "name": "shard",
        "options": [],
        "query": {
          "query": "label_values(scylla_reactor_utilization,shard)",
          "refId": "StandardVariableQuery"
        },
        "refresh": 2,
        "regex": "",
        "skipUrlSync": false,
        "sort": 3,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "allValue": null,
        "class": "topk_limit",
        "current": {
          "tags": [],
          "text": "256",
          "value": "256"
        },
        "description": null,
        "error": null,
        "hide": 0,
        "includeAll": false,
        "label": "Filter Highest",
        "multi": false,
        "name": "topk",
        "options": [
          {
            "selected": false,
            "text": "0",
            "value": "0"
          },
          {
            "selected": false,
            "text": "5",
            "value": "5"
          },
          {
            "selected": false,
            "text": "10",
            "value": "10"
          },
          {
            "selected": false,
            "text": "20",
            "value": "20"
          },
          {
            "selected": false,
            "text": "50",
            "value": "50"
          },
          {
            "selected": false,
            "text": "100",
            "value": "100"
          },
          {
            "selected": true,
            "text": "256",
            "value": "256"
          },
          {
            "selected": false,
            "text": "512",
            "value": "512"
          },
          {
            "selected": false,
            "text": "1000",
            "value": "1000"
          },
          {
            "selected": false,
            "text": "10000",
            "value": "10000"
          }
        ],
        "query": "0, 5, 10, 20, 50, 100, 256, 500, 1000, 10000",
        "queryValue": "",
        "skipUrlSync": false,
        "type": "custom"
      },
      {
        "allValue": null,
        "class": "botomk_limit",
        "current": {
          "tags": [],
          "text": "0",
          "value": "0"
        },
        "description": null,
        "error": null,
        "hide": 0,
        "includeAll": false,
        "label": "Filter Lowest",
        "multi": false,
        "name": "bottomk",
        "options": [
          {
            "selected": true,
            "text": "0",
            "value": "0"
          },
          {
            "selected": false,
            "text": "5",
            "value": "5"
          },
          {
            "selected": false,
            "text": "10",
            "value": "10"
          },
          {
            "selected": false,
            "text": "20",
            "value": "20"
          },
          {
            "selected": false,
            "text": "50",
            "value": "50"
          },
          {
            "selected": false,
            "text": "100",
            "value": "100"
          },
          {
            "selected": true,
            "text": "256",
            "value": "256"
          },
          {
            "selected": false,
            "text": "512",
            "value": "512"
          },
          {
            "selected": false,
            "text": "1000",
            "value": "1000"
          },
          {
            "selected": false,
            "text": "10000",
            "value": "10000"
          }
        ],
        "query": "0, 5, 10, 20, 50, 100, 256, 500, 1000, 10000",
        "skipUrlSync": false,
        "type": "custom"
      },
      {
        "allValue": null,
        "class": "template_variable_custom",
        "current": {
          "text": "6-0-1",
          "value": "6-0-1"
        },
        "description": null,
        "error": null,
        "hide": 2,
        "includeAll": false,
        "label": null,
        "multi": false,
        "name": "dash_version",
        "options": [
          {
            "selected": true,
            "text": "6-0-1",
            "value": "6-0-1"
          }
        ],
        "query": "6-0-1",
        "skipUrlSync": false,
        "type": "custom"
      },
      {
        "allValue": null,
        "class": "template_variable_custom",
        "current": {
          "text": "6.0.1",
          "value": "6.0.1"
        },
        "description": null,
        "error": null,
        "hide": 2,
        "includeAll": false,
        "label": null,
        "multi": false,
        "name": "scylla_version",
        "options": [
          {
            "selected": true,
            "text": "6.0.1",
            "value": "6.0.1"
          }
        ],
        "query": "6.0.1",
        "skipUrlSync": false,
        "type": "custom"
      },
      {
        "allValue": null,
        "class": "monitor_version_var",
        "current": {
          "text": "master",
          "value": "master"
        },
        "description": null,
        "error": null,
        "hide": 2,
        "includeAll": false,
        "label": null,
        "multi": false,
        "name": "monitoring_version",
        "options": [
          {
            "selected": true,
            "text": "master",
            "value": "master"
          }
        ],
        "query": "master",
        "skipUrlSync": false,
        "type": "custom"
      }
    ]
  },
  "time": {
    "from": "now-15m",
    "to": "now"
  },
  "timepicker": {
    "now": true,
    "refresh_intervals": [
      "5s",
      "10s",
      "30s",
      "1m",
      "5m",
      "15m",
      "30m",
      "1h",
      "2h",
      "1d"
    ],
    "time_options": [
      "5m",
      "15m",
      "1h",
      "6h",
      "12h",
      "24h",
      "2d",
      "7d",
      "30d"
    ]
  },
  "timezone": "utc",
  "title": "Scylla CQL",
  "uid": "cql-6-0-1",
  "version": 2412
}
