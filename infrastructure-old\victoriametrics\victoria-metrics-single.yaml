---
apiVersion: v1
kind: ConfigMap
metadata:
  name: victoriametrics-single-dashboard
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: VictoriaMetrics
data:
  victoriametrics-single-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": {
              "type": "datasource",
              "uid": "grafana"
            },
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "target": {
              "limit": 100,
              "matchAny": false,
              "tags": [],
              "type": "dashboard"
            },
            "type": "dashboard"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$ds"
            },
            "enable": true,
            "expr": "sum(ALERTS{job=~\"$job\", instance=~\"$instance\", alertgroup=\"vmsingle\",alertstate=\"firing\",show_at=\"dashboard\"}) by(alertname)",
            "iconColor": "red",
            "name": "alerts",
            "titleFormat": "{{alertname}}"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$ds"
            },
            "enable": true,
            "expr": "sum(vm_app_version{job=~\"$job\", instance=~\"$instance\"}) by(version) unless (sum(vm_app_version{job=~\"$job\", instance=~\"$instance\"} offset $__interval) by(version))",
            "hide": true,
            "iconColor": "dark-blue",
            "name": "version",
            "textFormat": "{{version}}",
            "titleFormat": "Version change"
          }
        ]
      },
      "description": "Overview for single-node VictoriaMetrics v1.83.0 or higher",
      "editable": true,
      "gnetId": 10229,
      "graphTooltip": 1,
      "iteration": 1737383589845,
      "links": [
        {
          "icon": "doc",
          "tags": [],
          "targetBlank": true,
          "title": "Single server Wiki",
          "type": "link",
          "url": "https://docs.victoriametrics.com/"
        },
        {
          "icon": "external link",
          "tags": [],
          "targetBlank": true,
          "title": "Found a bug?",
          "type": "link",
          "url": "https://github.com/VictoriaMetrics/VictoriaMetrics/issues"
        },
        {
          "icon": "external link",
          "tags": [],
          "targetBlank": true,
          "title": "New releases",
          "tooltip": "",
          "type": "link",
          "url": "https://github.com/VictoriaMetrics/VictoriaMetrics/releases"
        }
      ],
      "panels": [
        {
          "collapsed": false,
          "datasource": {
            "type": "prometheus",
            "uid": "$ds"
          },
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 6,
          "panels": [],
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "refId": "A"
            }
          ],
          "title": "Stats",
          "type": "row"
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "$ds"
          },
          "description": "",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 4,
            "x": 0,
            "y": 1
          },
          "id": 85,
          "options": {
            "code": {
              "language": "plaintext",
              "showLineNumbers": false,
              "showMiniMap": false
            },
            "content": "<div style=\"text-align: center;\">$version</div>",
            "mode": "markdown"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "refId": "A"
            }
          ],
          "title": "Version",
          "type": "text"
        },
        {
          "datasource": "VictoriaMetrics",
          "description": "How many datapoints are in storage",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 5,
            "x": 4,
            "y": 1
          },
          "id": 26,
          "maxDataPoints": 100,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "showPercentChange": false,
            "text": {},
            "textMode": "auto",
            "wideLayout": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(vm_rows{job=~\"$job\", instance=~\"$instance\", type!~\"indexdb.*\"})",
              "format": "time_series",
              "instant": true,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "title": "Total datapoints",
          "type": "stat"
        },
        {
          "datasource": "VictoriaMetrics",
          "description": "Shows the datapoints ingestion rate.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 5,
            "x": 9,
            "y": 1
          },
          "id": 107,
          "maxDataPoints": 100,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "showPercentChange": false,
            "text": {},
            "textMode": "auto",
            "wideLayout": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(rate(vm_rows_inserted_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval]))",
              "format": "time_series",
              "instant": true,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "title": "Ingestion rate",
          "type": "stat"
        },
        {
          "datasource": "${ds}",
          "description": "Shows the rate of HTTP read requests.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 5,
            "x": 14,
            "y": 1
          },
          "id": 108,
          "maxDataPoints": 100,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "showPercentChange": false,
            "text": {},
            "textMode": "auto",
            "wideLayout": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(rate(vm_http_requests_total{job=~\"$job\", instance=~\"$instance\", path!~\".*(/write|/metrics)\"}[$__rate_interval]))",
              "format": "time_series",
              "instant": true,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "title": "Read requests",
          "type": "stat"
        },
        {
          "datasource": "${ds}",
          "description": "Total number of available CPUs for VM process",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 5,
            "x": 19,
            "y": 1
          },
          "id": 77,
          "maxDataPoints": 100,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "showPercentChange": false,
            "text": {},
            "textMode": "auto",
            "wideLayout": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "exemplar": false,
              "expr": "sum(vm_available_cpu_cores{job=~\"$job\", instance=~\"$instance\"})",
              "format": "time_series",
              "instant": true,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "title": "Available CPU",
          "type": "stat"
        },
        {
          "datasource": "${ds}",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "red",
                    "value": null
                  },
                  {
                    "color": "green",
                    "value": 1800
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 4,
            "x": 0,
            "y": 3
          },
          "id": 87,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "showPercentChange": false,
            "text": {},
            "textMode": "auto",
            "wideLayout": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "min(vm_app_uptime_seconds{job=~\"$job\", instance=~\"$instance\"})",
              "instant": true,
              "interval": "",
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "title": "Uptime",
          "type": "stat"
        },
        {
          "datasource": "VictoriaMetrics",
          "description": "Shows the number of [active time series](https://docs.victoriametrics.com/faq/#what-is-an-active-time-series) with new data points inserted during the last hour. High value may result in ingestion slowdown.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 5,
            "x": 4,
            "y": 3
          },
          "id": 38,
          "maxDataPoints": 100,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "showPercentChange": false,
            "text": {},
            "textMode": "auto",
            "wideLayout": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(vm_cache_entries{job=~\"$job\", instance=~\"$instance\", type=\"storage/hour_metric_ids\"})",
              "format": "time_series",
              "instant": true,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "title": "Active series",
          "type": "stat"
        },
        {
          "datasource": "${ds}",
          "description": "Total amount of used disk space",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  }
                ]
              },
              "unit": "bytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 5,
            "x": 9,
            "y": 3
          },
          "id": 81,
          "maxDataPoints": 100,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "showPercentChange": false,
            "text": {},
            "textMode": "auto",
            "wideLayout": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(vm_data_size_bytes{job=~\"$job\", instance=~\"$instance\"})",
              "format": "time_series",
              "instant": true,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "title": "Disk space usage",
          "type": "stat"
        },
        {
          "datasource": "${ds}",
          "description": "Average disk usage per datapoint.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  }
                ]
              },
              "unit": "bytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 5,
            "x": 14,
            "y": 3
          },
          "id": 82,
          "maxDataPoints": 100,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "showPercentChange": false,
            "text": {},
            "textMode": "auto",
            "wideLayout": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "max(sum(vm_data_size_bytes{job=~\"$job\", instance=~\"$instance\"}) / sum(vm_rows{job=~\"$job\", instance=~\"$instance\"}))",
              "format": "time_series",
              "instant": true,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "title": "Bytes per point",
          "type": "stat"
        },
        {
          "datasource": "${ds}",
          "description": "Total size of available memory for VM process",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  }
                ]
              },
              "unit": "bytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 5,
            "x": 19,
            "y": 3
          },
          "id": 78,
          "maxDataPoints": 100,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "showPercentChange": false,
            "text": {},
            "textMode": "auto",
            "wideLayout": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "datasource": {
                "uid": "$ds"
              },
              "exemplar": false,
              "expr": "sum(vm_available_memory_bytes{job=~\"$job\", instance=~\"$instance\"})",
              "format": "time_series",
              "instant": true,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "title": "Available memory",
          "type": "stat"
        },
        {
          "collapsed": false,
          "datasource": {
            "type": "prometheus",
            "uid": "$ds"
          },
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 5
          },
          "id": 24,
          "panels": [],
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "refId": "A"
            }
          ],
          "title": "Overview",
          "type": "row"
        },
        {
          "datasource": "VictoriaMetrics",
          "description": "How many datapoints are inserted into storage per second",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 6
          },
          "id": 106,
          "options": {
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true,
              "sortBy": "Last *",
              "sortDesc": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "desc"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "9.1.0",
          "targets": [
            {
              "datasource": {
                "uid": "$ds"
              },
              "editorMode": "code",
              "expr": "sum(rate(vm_rows_inserted_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (type, instance) > 0",
              "format": "time_series",
              "hide": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "{{instance}} - {{type}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Datapoints ingestion rate",
          "type": "timeseries"
        },
        {
          "datasource": "${ds}",
          "description": "* `*` - unsupported query path\n* `/write` - insert into VM\n* `/metrics` - query VM system metrics\n* `/query` - query instant values\n* `/query_range` - query over a range of time\n* `/series` - match a certain label set\n* `/label/{}/values` - query a list of label values (variables mostly)",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 6
          },
          "id": 12,
          "options": {
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true,
              "sortBy": "Last *",
              "sortDesc": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "desc"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "9.1.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "editorMode": "code",
              "expr": "sum(rate(vm_http_requests_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (path, instance) > 0",
              "format": "time_series",
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "{{instance}} - {{path}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Requests rate",
          "type": "timeseries"
        },
        {
          "datasource": "${ds}",
          "description": "Shows the number of [active time series](https://docs.victoriametrics.com/faq/#what-is-an-active-time-series) with new data points inserted during the last hour. High value may result in ingestion slowdown. \n\nSee following link for details:",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 14
          },
          "id": 51,
          "links": [
            {
              "targetBlank": true,
              "title": "troubleshooting",
              "url": "https://docs.victoriametrics.com/troubleshooting"
            }
          ],
          "options": {
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "9.1.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "editorMode": "code",
              "expr": "vm_cache_entries{job=~\"$job\", instance=~\"$instance\", type=\"storage/hour_metric_ids\"}",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "{{instance}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Active time series",
          "type": "timeseries"
        },
        {
          "datasource": "${ds}",
          "description": "The less time it takes is better.\n* `*` - unsupported query path\n* `/write` - insert into VM\n* `/metrics` - query VM system metrics\n* `/query` - query instant values\n* `/query_range` - query over a range of time\n* `/series` - match a certain label set\n* `/label/{}/values` - query a list of label values (variables mostly)",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 14
          },
          "id": 22,
          "options": {
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true,
              "sortBy": "Last *",
              "sortDesc": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "desc"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "9.1.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "editorMode": "code",
              "expr": "max(vm_request_duration_seconds{job=~\"$job\", instance=~\"$instance\", quantile=\"0.99\"}) by (instance, path) > 0",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "{{instance}} - {{path}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Query duration 0.99 quantile",
          "type": "timeseries"
        },
        {
          "datasource": "${ds}",
          "description": "* `*` - unsupported query path\n* `/write` - insert into VM\n* `/metrics` - query VM system metrics\n* `/query` - query instant values\n* `/query_range` - query over a range of time\n* `/series` - match a certain label set\n* `/label/{}/values` - query a list of label values (variables mostly)",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "links": [],
              "mappings": [],
              "min": 0,
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 22
          },
          "id": 35,
          "options": {
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "desc"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "9.1.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "sum(rate(vm_http_request_errors_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance, path) > 0",
              "format": "time_series",
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "{{instance}} - {{path}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Requests error rate",
          "type": "timeseries"
        },
        {
          "datasource": "${ds}",
          "description": "Shows the rate of logging the messages by their level. Unexpected spike in rate is a good reason to check logs.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "bars",
                "fillOpacity": 100,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "auto",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 22
          },
          "id": 110,
          "options": {
            "legend": {
              "calcs": [
                "lastNotNull",
                "mean",
                "max"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true,
              "sortBy": "Last *",
              "sortDesc": true
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "editorMode": "code",
              "expr": "sum(rate(vm_log_messages_total{job=~\"$job\", instance=~\"$instance\", level!=\"info\"}[$__rate_interval])) by (instance, level, location) > 0",
              "interval": "5m",
              "legendFormat": "{{instance}} - {{level}}: {{location}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Logging rate",
          "type": "timeseries"
        },
        {
          "collapsed": true,
          "datasource": {
            "type": "prometheus",
            "uid": "$ds"
          },
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 30
          },
          "id": 46,
          "panels": [
            {
              "datasource": "VictoriaMetrics",
              "description": "Percentage of used RSS memory (resident).\nThe RSS memory shows the amount of memory recently accessed by the application. It includes anonymous memory and data from recently accessed files (aka page cache).\nThe application's performance will significantly degrade when memory usage is close to 100%.\n\nClick on the line and choose Drilldown to show memory usage per instance",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "percentunit"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 31
              },
              "id": 112,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "max(\n    max_over_time(process_resident_memory_bytes{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])\n    /\n    vm_available_memory_bytes{job=~\"$job\", instance=~\"$instance\"}\n) by(instance)",
                  "interval": "",
                  "legendFormat": "__auto",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "RSS memory % usage",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "bytes"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 31
              },
              "id": 44,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(go_memstats_sys_bytes{job=~\"$job\", instance=~\"$instance\"}) by (instance) + sum(vm_cache_size_bytes{job=~\"$job\", instance=~\"$instance\"}) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - requested from system",
                  "range": true,
                  "refId": "A"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(go_memstats_heap_inuse_bytes{job=~\"$job\", instance=~\"$instance\"}) by (instance) + sum(vm_cache_size_bytes{job=~\"$job\", instance=~\"$instance\"}) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - heap inuse",
                  "range": true,
                  "refId": "B"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(go_memstats_stack_inuse_bytes{job=~\"$job\", instance=~\"$instance\"}) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - stack inuse",
                  "range": true,
                  "refId": "C"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(process_resident_memory_bytes{job=~\"$job\", instance=~\"$instance\"}) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - resident",
                  "range": true,
                  "refId": "D"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(process_resident_memory_anon_bytes{job=~\"$job\", instance=~\"$instance\"}) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - resident anonymous",
                  "range": true,
                  "refId": "E"
                }
              ],
              "title": "Memory usage",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Share for memory allocated by the process itself. When memory usage reaches 100% it will be likely OOM-killed.\nSafe memory usage % considered to be below 80%\n\nClick on the line and choose Drilldown to show memory usage per instance",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "percentunit"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 39
              },
              "id": 123,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "max(\n    max_over_time(process_resident_memory_anon_bytes{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])\n    /\n    vm_available_memory_bytes{job=~\"$job\", instance=~\"$instance\"}\n) by(instance)",
                  "interval": "",
                  "legendFormat": "__auto",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "RSS anonymous memory % usage",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "percentunit"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 39
              },
              "id": 114,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "max(\n    rate(process_cpu_seconds_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])\n    /\n    vm_available_cpu_cores{job=~\"$job\", instance=~\"$instance\"}\n) by(instance)",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "__auto",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "CPU % usage",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows the percentage of open file descriptors compared to the limit set in the OS.\nReaching the limit of open files can cause various issues and must be prevented.\n\nSee how to change limits here https://medium.com/@muhammadtriwibowo/set-permanently-ulimit-n-open-files-in-ubuntu-4d61064429a",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 2,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "percentunit"
                },
                "overrides": [
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "max"
                    },
                    "properties": [
                      {
                        "id": "color",
                        "value": {
                          "fixedColor": "#C4162A",
                          "mode": "fixed"
                        }
                      }
                    ]
                  }
                ]
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 47
              },
              "id": 75,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "max_over_time(process_open_fds{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])\n/\nprocess_max_fds{job=~\"$job\", instance=~\"$instance\"}",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 2,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Open FDs",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "CPU cores used by instance",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": [
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "Limit"
                    },
                    "properties": [
                      {
                        "id": "color",
                        "value": {
                          "fixedColor": "#F2495C",
                          "mode": "fixed"
                        }
                      }
                    ]
                  }
                ]
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 47
              },
              "id": 57,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "rate(process_cpu_seconds_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "min(process_cpu_cores_available{job=~\"$job\", instance=~\"$instance\"})",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "Limit",
                  "range": true,
                  "refId": "B"
                }
              ],
              "title": "CPU",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 0,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 55
              },
              "id": 47,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(go_goroutines{job=~\"$job\", instance=~\"$instance\"}) by(instance)",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Goroutines",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows the number of bytes read/write from the storage layer.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "bytes"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 55
              },
              "id": 76,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(rate(process_io_storage_read_bytes_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - read",
                  "range": true,
                  "refId": "A"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(rate(process_io_storage_written_bytes_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - write",
                  "range": true,
                  "refId": "B"
                }
              ],
              "title": "Disk writes/reads",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 0,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 63
              },
              "id": 48,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(process_num_threads{job=~\"$job\", instance=~\"$instance\"}) by(instance)",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Threads",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows the number of read/write syscalls such as read, pread, write, pwrite.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 63
              },
              "id": 124,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(rate(process_io_read_syscalls_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - read calls",
                  "range": true,
                  "refId": "A"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(rate(process_io_write_syscalls_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - write calls",
                  "range": true,
                  "refId": "B"
                }
              ],
              "title": "Disk write/read calls",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 71
              },
              "id": 49,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(rate(vm_tcplistener_accepts_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by(instance)",
                  "format": "time_series",
                  "hide": false,
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "TCP connections rate",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 71
              },
              "id": 37,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(vm_tcplistener_conns{job=~\"$job\", instance=~\"$instance\"}) by(instance)",
                  "format": "time_series",
                  "hide": false,
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "TCP connections",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows network usage by VM:\n* Writes show traffic sent to clients\n* Reads show traffic received from clients",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "decbits"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 79
              },
              "id": 127,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(rate(vm_tcplistener_read_bytes_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by(name) * 8 > 0",
                  "format": "time_series",
                  "hide": false,
                  "intervalFactor": 1,
                  "legendFormat": "read via {{name}}",
                  "range": true,
                  "refId": "A"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(rate(vm_tcplistener_written_bytes_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by(name) * 8 > 0",
                  "format": "time_series",
                  "hide": false,
                  "intervalFactor": 1,
                  "legendFormat": "write via {{name}}",
                  "range": true,
                  "refId": "B"
                }
              ],
              "title": "Network usage",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows the percent of CPU spent on garbage collection.\n\nIf % is high, then CPU usage can be decreased by changing GOGC to higher values. Increasing GOGC value will increase memory usage, and decrease CPU usage.\n\nTry searching for keyword `GOGC` at https://docs.victoriametrics.com/troubleshooting/ ",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 0,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "percentunit"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 79
              },
              "id": 125,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.2.6",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "max(\n  rate(go_gc_cpu_seconds_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval]) \n / rate(process_cpu_seconds_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])\n ) by(instance)",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 2,
                  "legendFormat": "__auto",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "CPU spent on GC",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows the time goroutines have spent in runnable state before actually running. The lower is better.\n\nHigh values or values exceeding the threshold is usually a sign of            insufficient CPU resources or CPU throttling. \n\nVerify that service has enough CPU resources. Otherwise, the service could work unreliably with delays in processing.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 0,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 0.1
                      }
                    ]
                  },
                  "unit": "s"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 87
              },
              "id": 128,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.2.6",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "histogram_quantile(0.99, sum(rate(go_sched_latencies_seconds_bucket{job=~\"$job\"}[$__rate_interval])) by (job, instance, le))",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 2,
                  "legendFormat": "__auto",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Go scheduling latency",
              "type": "timeseries"
            }
          ],
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "refId": "A"
            }
          ],
          "title": "Resource usage",
          "type": "row"
        },
        {
          "collapsed": true,
          "datasource": {
            "type": "prometheus",
            "uid": "$ds"
          },
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 31
          },
          "id": 71,
          "panels": [
            {
              "datasource": "${ds}",
              "description": "Shows the rate and total number of new series created over last 24h.\n\nHigh [churn rate](https://docs.victoriametrics.com/faq/#what-is-high-churn-rate) tightly connected with database performance and may result in unexpected OOM's or slow queries. It is recommended to always keep an eye on this metric to avoid unexpected [cardinality](https://docs.victoriametrics.com/keyconcepts/#cardinality) \"explosions\".\n\nThe higher churn rate is, the more resources required to handle it. Consider to keep the churn rate as low as possible.\n\nGood references to read:\n* https://www.robustperception.io/cardinality-is-key\n* https://www.robustperception.io/using-tsdb-analyze-to-investigate-churn-and-cardinality",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 32
              },
              "id": 66,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(rate(vm_new_timeseries_created_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance)",
                  "interval": "",
                  "legendFormat": "{{instance}} - churn rate",
                  "range": true,
                  "refId": "A"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(increase(vm_new_timeseries_created_total{job=~\"$job\", instance=~\"$instance\"}[24h])) by (instance)",
                  "interval": "",
                  "legendFormat": "{{instance}} - new series over 24h",
                  "range": true,
                  "refId": "B"
                }
              ],
              "title": "Churn rate",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "The percentage of slow inserts comparing to total insertion rate during the last 5 minutes. \n\nThe less value is better. If percentage remains high (>10%) during extended periods of time, then it is likely more RAM is needed for optimal handling of the current number of [active time series](https://docs.victoriametrics.com/faq/#what-is-an-active-time-series). \n\nIn general, VictoriaMetrics requires ~1KB or RAM per active time series, so it should be easy calculating the required amounts of RAM for the current workload according to capacity planning docs. But the resulting number may be far from the real number because the required amounts of memory depends on many other factors such as the number of labels per time series and the length of label values. See also [this issue](https://github.com/VictoriaMetrics/VictoriaMetrics/issues/3976#issuecomment-1476883183) for details.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 2,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "transparent",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 0.1
                      }
                    ]
                  },
                  "unit": "percentunit"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 32
              },
              "id": 68,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "max(\n    rate(vm_slow_row_inserts_total{job=~\"$job\"}[$__rate_interval]) \n    / rate(vm_rows_added_to_storage_total{job=~\"$job\"}[$__rate_interval])\n) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Slow inserts %",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Merge assist happens when storage can't keep up with merging parts. This is usually a sign of overload for storage.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "bars",
                    "fillOpacity": 100,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 40
              },
              "id": 116,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(increase(vm_assisted_merges_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by(instance, type) > 0",
                  "format": "time_series",
                  "interval": "5m",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - {{type}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Assisted merges",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Slow queries rate according to `search.logSlowQueryDuration` flag, which is `5s` by default.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 40
              },
              "id": 60,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(rate(vm_slow_queries_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Slow queries rate",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows the percentage of used cache size from the allowed size by type. \nValues close to 100% show the maximum potential utilization.\nValues close to 0% show that cache is underutilized.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "mappings": [],
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "percentunit"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 9,
                "w": 12,
                "x": 0,
                "y": 48
              },
              "id": 90,
              "options": {
                "legend": {
                  "calcs": [
                    "lastNotNull",
                    "mean",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "vm_cache_size_bytes{job=~\"$job\", instance=~\"$instance\"} / vm_cache_size_max_bytes{job=~\"$job\", instance=~\"$instance\"}",
                  "interval": "",
                  "legendFormat": "{{instance}} - {{type}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Cache usage % by type",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows cache miss ratio. Lower is better.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "max": 1,
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "percentunit"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 9,
                "w": 12,
                "x": 12,
                "y": 48
              },
              "id": 118,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "(\n    rate(vm_cache_misses_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])\n    /\n    rate(vm_cache_requests_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])\n) > 0",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - {{type}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Cache miss ratio",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Flags explicitly set to non-default values",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "thresholds"
                  },
                  "custom": {
                    "align": "auto",
                    "cellOptions": {},
                    "filterable": false
                  },
                  "mappings": [],
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  }
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 57
              },
              "id": 126,
              "options": {
                "cellHeight": "sm",
                "footer": {
                  "countRows": false,
                  "fields": "",
                  "reducer": [
                    "sum"
                  ],
                  "show": false
                },
                "showHeader": true
              },
              "pluginVersion": "7.4.2",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(flag{is_set=\"true\", job=~\"$job\", instance=~\"$instance\"}) by(job, instance, name, value)",
                  "format": "table",
                  "instant": true,
                  "legendFormat": "__auto",
                  "range": false,
                  "refId": "A"
                }
              ],
              "title": "Non-default flags",
              "type": "table"
            },
            {
              "datasource": "${ds}",
              "description": "VictoriaMetrics limits the number of labels per each metric with `-maxLabelsPerTimeseries` command-line flag.\n\nThis prevents from ingesting metrics with too many labels. The value of `maxLabelsPerTimeseries` must be adjusted for your workload.\n\nWhen limit is exceeded (graph is > 0) - extra labels are dropped, which could result in unexpected identical time series. See more details about dropped labels in logs.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 2,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 57
              },
              "id": 74,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "list",
                  "placement": "bottom",
                  "showLegend": false
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "exemplar": false,
                  "expr": "sum(increase(vm_metrics_with_dropped_labels_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - limit exceeded",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Labels limit exceeded",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows the number of restarts per job. The chart can be useful to identify periodic process restarts and correlate them with potential issues or anomalies. Normally, processes shouldn't restart unless restart was inited by user. The reason of restarts should be figured out by checking the logs of each specific service. ",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "axisSoftMin": 0,
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "stepAfter",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 0,
                  "links": [],
                  "mappings": [],
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "none"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 65
              },
              "id": 129,
              "options": {
                "legend": {
                  "calcs": [
                    "lastNotNull"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(changes(vm_app_start_timestamp{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval]) > 0) by(job)",
                  "format": "time_series",
                  "instant": false,
                  "legendFormat": "{{job}}",
                  "refId": "A"
                }
              ],
              "title": "Restarts ($job)",
              "type": "timeseries"
            }
          ],
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "refId": "A"
            }
          ],
          "title": "Troubleshooting",
          "type": "row"
        },
        {
          "collapsed": true,
          "datasource": {
            "type": "prometheus",
            "uid": "$ds"
          },
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 32
          },
          "id": 14,
          "panels": [
            {
              "datasource": "${ds}",
              "description": "How many datapoints are inserted into storage per second",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 33
              },
              "id": 10,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(rate(vm_rows_inserted_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance, type) > 0",
                  "format": "time_series",
                  "hide": false,
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - {{type}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Datapoints ingestion rate",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows the approx time needed to reach 100% of disk capacity based on the following params:\n* free disk space;\n* row ingestion rate;\n* compression.\n\nNote: this panel doesn't account for deduplication process.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "s"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 33
              },
              "id": 73,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "min"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "(vm_free_disk_space_bytes{job=~\"$job\", instance=~\"$instance\"}-vm_free_disk_space_limit_bytes{job=~\"$job\", instance=~\"$instance\"}) \n/ ignoring(path) (\n  rate(vm_rows_added_to_storage_total{job=~\"$job\", instance=~\"$instance\"}[1d]) \n  * scalar(\n    sum(vm_data_size_bytes{job=~\"$job\", instance=~\"$instance\", type!~\"indexdb.*\"}) \n    / sum(vm_rows{job=~\"$job\", instance=~\"$instance\", type!~\"indexdb.*\"})\n    )\n  )",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Storage full ETA",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows amount of on-disk space occupied by data points and the remaining disk space at `-storageDataPath`",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "bytes"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 41
              },
              "id": 53,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(vm_data_size_bytes{job=~\"$job\", instance=~\"$instance\", type!~\"indexdb.*\"}) by (instance)",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - Used (datapoints)",
                  "range": true,
                  "refId": "A"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "expr": "vm_free_disk_space_bytes{job=~\"$job\", instance=~\"$instance\"} - vm_free_disk_space_limit_bytes{job=~\"$job\", instance=~\"$instance\"}",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - Free",
                  "range": true,
                  "refId": "B"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(vm_data_size_bytes{job=~\"$job\", instance=~\"$instance\", type=~\"indexdb.*\"}) by (instance)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - Used (index)",
                  "range": true,
                  "refId": "C"
                }
              ],
              "title": "Disk space usage - datapoints",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "How many datapoints are in RAM queue waiting to be written into storage. The number of pending data points should be in the range from 0 to `3*<ingestion_rate>`, since VictoriaMetrics pushes pending data to persistent storage every two seconds.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": [
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "pending index entries"
                    },
                    "properties": [
                      {
                        "id": "unit",
                        "value": "none"
                      },
                      {
                        "id": "decimals",
                        "value": 3
                      }
                    ]
                  }
                ]
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 41
              },
              "id": 34,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "expr": "vm_pending_rows{job=~\"$job\", instance=~\"$instance\", type=\"storage\"}",
                  "format": "time_series",
                  "hide": false,
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - pending datapoints",
                  "range": true,
                  "refId": "A"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "expr": "vm_pending_rows{job=~\"$job\", instance=~\"$instance\", type=\"indexdb\"}",
                  "format": "time_series",
                  "hide": false,
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - pending index entries",
                  "range": true,
                  "refId": "B"
                }
              ],
              "title": "Pending datapoints",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows how many datapoints are in the storage and what is average disk usage per datapoint.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": [
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "bytes-per-datapoint"
                    },
                    "properties": [
                      {
                        "id": "unit",
                        "value": "bytes"
                      },
                      {
                        "id": "decimals",
                        "value": 2
                      }
                    ]
                  }
                ]
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 49
              },
              "id": 30,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(vm_rows{job=~\"$job\", instance=~\"$instance\", type!~\"indexdb.*\"}) by (instance)",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - total datapoints",
                  "range": true,
                  "refId": "A"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(vm_data_size_bytes{job=~\"$job\", instance=~\"$instance\"}) by (instance)\n/ sum(vm_rows{job=~\"$job\", instance=~\"$instance\", type!~\"indexdb.*\"}) by (instance)",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - bytes-per-datapoint",
                  "range": true,
                  "refId": "B"
                }
              ],
              "title": "Datapoints",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Data parts of LSM tree.\nHigh number of parts could be an evidence of slow merge performance - check the resource utilization.\n* `indexdb` - inverted index\n* `storage/small` - recently added parts of data ingested into storage(hot data)\n* `storage/big` -  small parts gradually merged into big parts (cold data)",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 49
              },
              "id": 36,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(vm_parts{job=~\"$job\", instance=~\"$instance\"}) by (instance, type)",
                  "format": "time_series",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - {{type}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "LSM parts",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows how many rows were ignored on insertion due to corrupted or out of retention timestamps.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 57
              },
              "id": 58,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "exemplar": false,
                  "expr": "sum(increase(vm_rows_ignored_total{job=~\"$job\", instance=~\"$instance\"}[1h])) by (instance, reason)",
                  "format": "time_series",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - {{reason}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Rows ignored for last 1h",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "The number of on-going merges in storage nodes.  It is expected to have high numbers for `storage/small` metric.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 0,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 57
              },
              "id": 62,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(vm_active_merges{job=~\"$job\", instance=~\"$instance\"}) by(instance, type)",
                  "legendFormat": "{{instance}} - {{type}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Active merges",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "Shows how many ongoing insertions (not API /write calls) on disk are taking place, where:\n* `max` - equal to number of CPUs;\n* `current` - current number of goroutines busy with inserting rows into underlying storage.\n\nEvery successful API /write call results into flush on disk. However, these two actions are separated and controlled via different concurrency limiters. The `max` on this panel can't be changed and always equal to number of CPUs. \n\nWhen `current` hits `max` constantly, it means storage is overloaded and requires more CPU.\n\n",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 0,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": [
                  {
                    "matcher": {
                      "id": "byName",
                      "options": "max"
                    },
                    "properties": [
                      {
                        "id": "color",
                        "value": {
                          "fixedColor": "#C4162A",
                          "mode": "fixed"
                        }
                      }
                    ]
                  }
                ]
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 65
              },
              "id": 59,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "max_over_time(vm_concurrent_insert_capacity{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - max",
                  "range": true,
                  "refId": "A"
                },
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(vm_concurrent_insert_current{job=~\"$job\", instance=~\"$instance\"}) by (instance)",
                  "format": "time_series",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}} - current",
                  "range": true,
                  "refId": "B"
                }
              ],
              "title": "Concurrent flushes on disk",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "The number of rows merged per second by storage nodes.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 0,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 65
              },
              "id": 64,
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "none"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "sum(rate(vm_rows_merged_total{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by(instance, type)",
                  "legendFormat": "{{instance}} - {{type}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Merge speed",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "99th percentile of number of series read per query.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 2,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 73
              },
              "id": 99,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "histogram_quantile(0.99, sum(rate(vm_series_read_per_query_bucket{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance, vmrange))",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Series read per query",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "99th percentile of number of [data samples](https://docs.victoriametrics.com/keyconcepts/#raw-samples) read per queried series.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 2,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 73
              },
              "id": 103,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "histogram_quantile(0.99, sum(rate(vm_rows_read_per_series_bucket{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance, vmrange))",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Datapoints read per series",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "99th percentile of number of [data samples](https://docs.victoriametrics.com/keyconcepts/#raw-samples) read per query.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 2,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 81
              },
              "id": 122,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "histogram_quantile(0.99, sum(rate(vm_rows_read_per_query_bucket{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance, vmrange))",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Datapoints read per query",
              "type": "timeseries"
            },
            {
              "datasource": "${ds}",
              "description": "99th percentile of number of [data samples](https://docs.victoriametrics.com/keyconcepts/#raw-samples) scanner per query.\n\nThis number can exceed number of RowsReadPerQuery if `step` query arg passed to [/api/v1/query_range](https://prometheus.io/docs/prometheus/latest/querying/api/#range-queries) is smaller than the lookbehind window set in square brackets of [rollup function](https://docs.victoriametrics.com/metricsql/#rollup-functions). For example, if `increase(some_metric[1h])` is executed with the `step=5m`, then the same [data samples](https://docs.victoriametrics.com/keyconcepts/#raw-samples) on a hour time range are scanned `1h/5m=12` times. See [this article](https://valyala.medium.com/how-to-optimize-promql-and-metricsql-queries-85a1b75bf986) for details.",
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 0,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false,
                      "viz": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": false,
                    "stacking": {},
                    "thresholdsStyle": {}
                  },
                  "decimals": 2,
                  "links": [],
                  "mappings": [],
                  "min": 0,
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 81
              },
              "id": 105,
              "links": [],
              "options": {
                "legend": {
                  "calcs": [
                    "mean",
                    "lastNotNull",
                    "max"
                  ],
                  "displayMode": "table",
                  "placement": "bottom",
                  "showLegend": true,
                  "sortBy": "Last *",
                  "sortDesc": true
                },
                "tooltip": {
                  "mode": "multi",
                  "sort": "desc"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "9.1.0",
              "targets": [
                {
                  "datasource": {
                    "type": "prometheus",
                    "uid": "$ds"
                  },
                  "editorMode": "code",
                  "expr": "histogram_quantile(0.99, sum(rate(vm_rows_scanned_per_query_bucket{job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])) by (instance, vmrange))",
                  "format": "time_series",
                  "interval": "",
                  "intervalFactor": 1,
                  "legendFormat": "{{instance}}",
                  "range": true,
                  "refId": "A"
                }
              ],
              "title": "Datapoints scanned per query",
              "type": "timeseries"
            }
          ],
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "$ds"
              },
              "refId": "A"
            }
          ],
          "title": "Storage",
          "type": "row"
        }
      ],
      "refresh": "",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [
        "victoriametrics",
        "vmsingle"
      ],
      "templating": {
        "list": [
          {
            "current": {
              "selected": false,
              "text": "VictoriaMetrics",
              "value": "VictoriaMetrics"
            },
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": null,
            "multi": false,
            "name": "ds",
            "options": [],
            "query": "prometheus",
            "queryValue": "",
            "refresh": 1,
            "regex": "VictoriaMetrics",
            "skipUrlSync": false,
            "type": "datasource"
          },
          {
            "allValue": null,
            "current": {
              "selected": true,
              "text": "victoria-metrics",
              "value": "victoria-metrics"
            },
            "datasource": "${ds}",
            "definition": "label_values(vm_app_version{version=~\"victoria-metrics-.*\"}, job)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": null,
            "multi": false,
            "name": "job",
            "options": [],
            "query": {
              "query": "label_values(vm_app_version{version=~\"victoria-metrics-.*\"}, job)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "v1.108.1",
              "value": "v1.108.1"
            },
            "datasource": "${ds}",
            "definition": "label_values(vm_app_version{job=~\"$job\", instance=~\"$instance\"},  version)",
            "description": null,
            "error": null,
            "hide": 2,
            "includeAll": false,
            "label": null,
            "multi": false,
            "name": "version",
            "options": [],
            "query": {
              "query": "label_values(vm_app_version{job=~\"$job\", instance=~\"$instance\"},  version)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "/.*-(?:tags|heads)-(.*)-(?:0|dirty)-.*/",
            "skipUrlSync": false,
            "sort": 2,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": ".*",
            "current": {
              "selected": true,
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": "${ds}",
            "definition": "label_values(vm_app_version{job=~\"$job\"}, instance)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": null,
            "multi": true,
            "name": "instance",
            "options": [],
            "query": {
              "query": "label_values(vm_app_version{job=~\"$job\"}, instance)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "datasource": "${ds}",
            "description": null,
            "error": null,
            "filters": [],
            "hide": 0,
            "label": null,
            "name": "adhoc",
            "skipUrlSync": false,
            "type": "adhoc"
          }
        ]
      },
      "time": {
        "from": "now-24h",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "",
      "title": "VictoriaMetrics - single-node",
      "uid": "wNf0q_kZk",
      "version": 12
    }
