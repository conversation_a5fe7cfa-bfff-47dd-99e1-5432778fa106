# PowerShell script to replace "datasource": "Prometheus", with proper object format
param(
    [string]$TargetDir = "infrastructure"
)

function Update-PrometheusDataSource {
    param([string]$FilePath)
    
    try {
        Write-Host "Processing: $FilePath"
        
        # Read the file content
        $content = Get-Content $FilePath -Raw -Encoding UTF8
        
        # Check if file contains the pattern we want to replace
        if ($content -match '"datasource":\s*"Prometheus"') {
            Write-Host "  Found Prometheus datasource references"
            
            # Replace the pattern with proper object format
            # This regex handles various whitespace scenarios
            $updatedContent = $content -replace '"datasource":\s*"Prometheus"', '"datasource": {
        "type": "prometheus"
      }'
            
            # Validate that it's still valid JSON by trying to parse it
            try {
                $null = $updatedContent | ConvertFrom-Json
                Write-Host "  JSON validation passed"
            }
            catch {
                Write-Warning "  JSON validation failed for $FilePath - skipping"
                return $false
            }
            
            # Write back as UTF-8 without BOM
            $utf8NoBom = New-Object System.Text.UTF8Encoding $false
            [System.IO.File]::WriteAllText($FilePath, $updatedContent, $utf8NoBom)
            
            Write-Host "  Successfully updated: $FilePath" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "  No Prometheus datasource references found"
            return $false
        }
    }
    catch {
        Write-Warning "Error processing file $FilePath : $($_.Exception.Message)"
        return $false
    }
}

function Process-Directory {
    param([string]$Directory)
    
    Write-Host "Scanning directory: $Directory" -ForegroundColor Yellow
    
    # Find all dashboard.json files recursively
    $jsonFiles = Get-ChildItem -Path $Directory -Name "dashboard.json" -Recurse
    
    $processedCount = 0
    $updatedCount = 0
    
    foreach ($file in $jsonFiles) {
        $fullPath = Join-Path $Directory $file
        $processedCount++
        
        if (Update-PrometheusDataSource $fullPath) {
            $updatedCount++
        }
    }
    
    Write-Host "`nSummary:" -ForegroundColor Cyan
    Write-Host "Processed: $processedCount files"
    Write-Host "Updated: $updatedCount files"
}

# Main execution
if (Test-Path $TargetDir) {
    Write-Host "Starting Prometheus datasource update..." -ForegroundColor Cyan
    Process-Directory $TargetDir
    Write-Host "`nPrometheus datasource update completed!" -ForegroundColor Green
} else {
    Write-Error "Target directory not found: $TargetDir"
}
