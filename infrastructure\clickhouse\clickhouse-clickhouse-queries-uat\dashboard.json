﻿{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": {
          "type": "datasource",
          "uid": "grafana"
        },
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      }
    ]
  },
  "description": "Helps to visualize most frequent, slowest, failed queries.\r\nShows queries rate per second, table with last queries",
  "editable": true,
  "fiscalYearStartMonth": 0,
  "graphTooltip": 1,
  "id": 4055,
  "links": [],
  "panels": [
    {
      "collapsed": false,
      "gridPos": {
        "h": 1,
        "w": 24,
        "x": 0,
        "y": 0
      },
      "id": 22,
      "panels": [],
      "title": "Top charts",
      "type": "row"
    },
    {
      "datasource": {
        "type": "vertamedia-clickhouse-datasource",
        "uid": "cef0o5s7s6z9ce"
      },
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisBorderShow": false,
            "axisCenteredZero": false,
            "axisColorMode": "text",
            "axisLabel": "",
            "axisPlacement": "auto",
            "barAlignment": 0,
            "barWidthFactor": 0.6,
            "drawStyle": "line",
            "fillOpacity": 10,
            "gradientMode": "none",
            "hideFrom": {
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "insertNulls": false,
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {
              "group": "A",
              "mode": "none"
            },
            "thresholdsStyle": {
              "mode": "off"
            }
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 7,
        "w": 20,
        "x": 0,
        "y": 1
      },
      "id": 15,
      "options": {
        "alertThreshold": true,
        "legend": {
          "calcs": [
            "mean",
            "max"
          ],
          "displayMode": "table",
          "placement": "right",
          "showLegend": true,
          "sortBy": "Mean",
          "sortDesc": true
        },
        "tooltip": {
          "mode": "multi",
          "sort": "desc"
        }
      },
      "pluginVersion": "11.3.0",
      "targets": [
        {
          "adHocFilters": [],
          "adHocValuesQuery": "",
          "add_metadata": true,
          "contextWindowSize": "10",
          "database": "system",
          "datasource": {
            "type": "vertamedia-clickhouse-datasource",
            "uid": "cef0o5s7s6z9ce"
          },
          "dateColDataType": "event_date",
          "dateLoading": false,
          "dateTimeColDataType": "event_time",
          "datetimeLoading": false,
          "editorMode": "sql",
          "expr": "",
          "extrapolate": true,
          "format": "time_series",
          "formattedQuery": "<font color=\"darkcyan\">$rateColumns</font>(<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">substring</font>(query,Ã‚ Ã‚ <font color=\"cornflowerblue\">1</font>,Ã‚ Ã‚ <font color=\"cornflowerblue\">45</font>) <font color=\"darkorange\">AS</font> query,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">count</font>() c)<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">cityHash64</font>(query) <font color=\"darkorange\">global</font> <font color=\"darkorange\">in</font> (<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkorange\">SELECT</font> <font color=\"navajowhite\">cityHash64</font>(<font color=\"navajowhite\">substring</font>(query,Ã‚ Ã‚ <font color=\"cornflowerblue\">1</font>,Ã‚ Ã‚ <font color=\"cornflowerblue\">45</font>)) <font color=\"darkorange\">AS</font> h<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkorange\">WHERE</font><br />Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkcyan\">$timeFilter</font><br />Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">AND</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">AND</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Ã‚ Ã‚ <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkorange\">GROUP BY</font> h<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkorange\">ORDER BY</font> <font color=\"navajowhite\">count</font>() <font color=\"darkorange\">desc</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkorange\">LIMIT</font> <font color=\"darkcyan\">$top</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">AND</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">AND</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Ã‚ Ã‚ <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))",
          "interval": "",
          "intervalFactor": 2,
          "query": "SELECT t, arrayMap(a -> (a.1, a.2/runningDifference(t/1000)), groupArr) \nFROM (\n  SELECT t, groupArray((query, c)) AS groupArr \n  FROM (\n    SELECT (intDiv(toUInt32(event_time), 2) * 2) * 1000 AS t, \n           substring(query, 1, 45) AS query, \n           count() AS c \n    FROM $table\n    WHERE $timeFilter\n      AND cityHash64(query) global in (\n        SELECT cityHash64(substring(query, 1, 45)) AS h\n        FROM $table\n        WHERE $timeFilter\n          AND type in ($type)\n          AND initial_user in ($user)\n          AND('$query_type' = 'all' or(positionCaseInsensitive(query, '$query_type') = 1))\n        GROUP BY h\n        ORDER BY count() desc\n        LIMIT $top\n      )\n      AND type in ($type)\n      AND initial_user in ($user)\n      AND('$query_type' = 'all' or(positionCaseInsensitive(query, '$query_type') = 1))\n    GROUP BY t, query\n    ORDER BY t, query\n  ) \n  GROUP BY t \n  ORDER BY t\n)",
          "rawQuery": "/* grafana dashboard=ClickHouse Queries UAT, user=1 */\nSELECT t, arrayMap(a -> (a.1, a.2/(t/1000 - lagInFrame(t/1000,1,0) OVER ())), groupArr) FROM (SELECT t, groupArray((query, c)) AS groupArr FROM ( SELECT (intDiv(toUInt32(event_time), 2) * 2) * 1000 AS t, substring(query, 1, 45) AS query, count() c FROM system.query_log\nWHERE event_date >= toDate(1741597130) AND event_date <= toDate(1741598930) AND event_time >= toDateTime(1741597130) AND event_time <= toDateTime(1741598930) AND\n    cityHash64(query) global in (\n    SELECT cityHash64(substring(query,  1,  45)) AS h\n    FROM system.query_log\n    WHERE event_date >= toDate(1741597130) AND event_date <= toDate(1741598930) AND event_time >= toDateTime(1741597130) AND event_time <= toDateTime(1741598930) AND\n        event_date >= toDate(1741597130) AND event_date <= toDate(1741598930) AND event_time >= toDateTime(1741597130) AND event_time <= toDateTime(1741598930)\n        AND type in (1,2,3,4)\n        AND initial_user in ('gaming','service_reporting_legislation','prometheus','service_ps','reporting','team:reporting','default','team-crm-app','backup','andrey.shumilov','migrations','','team:qa','service_grafana')\n        AND('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1))\n    GROUP BY h\n    ORDER BY count() desc\n    LIMIT 5)\n    AND type in (1,2,3,4)\n    AND initial_user in ('gaming','service_reporting_legislation','prometheus','service_ps','reporting','team:reporting','default','team-crm-app','backup','andrey.shumilov','migrations','','team:qa','service_grafana')\n    AND('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1))) GROUP BY t ORDER BY t)",
          "refId": "A",
          "resultFormat": "time_series",
          "round": "0s",
          "skip_comments": true,
          "table": "query_log",
          "tableLoading": false,
          "useWindowFuncForMacros": true
        }
      ],
      "title": "Top $top request's rate by type: $type; user: $user; query type: $query_type",
      "type": "timeseries"
    },
    {
      "fieldConfig": {
        "defaults": {},
        "overrides": []
      },
      "gridPos": {
        "h": 7,
        "w": 4,
        "x": 20,
        "y": 1
      },
      "id": 17,
      "options": {
        "code": {
          "language": "plaintext",
          "showLineNumbers": false,
          "showMiniMap": false
        },
        "content": "1 - successful start of query execution\n\n2 - successful end of query execution\n\n3 - exception before start of query execution\n\n4 - exception while query execution",
        "mode": "markdown"
      },
      "pluginVersion": "11.3.0",
      "title": "Types",
      "transparent": true,
      "type": "text"
    },
    {
      "datasource": {
        "type": "vertamedia-clickhouse-datasource",
        "uid": "cef0o5s7s6z9ce"
      },
      "fieldConfig": {
        "defaults": {
          "custom": {
            "cellOptions": {
              "type": "auto"
            },
            "inspect": false
          },
          "decimals": 2,
          "displayName": "",
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": [
          {
            "matcher": {
              "id": "byName",
              "options": "Time"
            },
            "properties": [
              {
                "id": "displayName",
                "value": "Time"
              },
              {
                "id": "custom.hidden",
                "value": true
              },
              {
                "id": "custom.align"
              }
            ]
          },
          {
            "matcher": {
              "id": "byName",
              "options": "duration"
            },
            "properties": [
              {
                "id": "displayName",
                "value": "duration"
              },
              {
                "id": "unit",
                "value": "ms"
              },
              {
                "id": "decimals",
                "value": 2
              },
              {
                "id": "custom.align"
              }
            ]
          },
          {
            "matcher": {
              "id": "byName",
              "options": "count"
            },
            "properties": [
              {
                "id": "unit",
                "value": "short"
              },
              {
                "id": "custom.align"
              }
            ]
          }
        ]
      },
      "gridPos": {
        "h": 11,
        "w": 8,
        "x": 0,
        "y": 8
      },
      "id": 18,
      "options": {
        "cellHeight": "sm",
        "footer": {
          "countRows": false,
          "fields": "",
          "reducer": [
            "sum"
          ],
          "show": false
        },
        "showHeader": true
      },
      "pluginVersion": "11.3.0",
      "targets": [
        {
          "database": "system",
          "datasource": {
            "type": "vertamedia-clickhouse-datasource",
            "uid": "cef0o5s7s6z9ce"
          },
          "dateColDataType": "event_date",
          "dateLoading": false,
          "dateTimeColDataType": "event_time",
          "datetimeLoading": false,
          "expr": "",
          "format": "table",
          "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">rand</font>() <font color=\"darkorange\">as</font> t,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">substring</font>(query,Ã‚ Ã‚ <font color=\"cornflowerblue\">1</font>,Ã‚ Ã‚ <font color=\"cornflowerblue\">70</font>) <font color=\"darkorange\">AS</font> query,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">avg</font>(query_duration_ms) duration,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">count</font>() <font color=\"navajowhite\">count</font><br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkcyan\">$timeFilter</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Ã‚ Ã‚ <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br /><font color=\"darkorange\">GROUP BY</font> query<br /><font color=\"darkorange\">ORDER BY</font> duration <font color=\"darkorange\">desc</font><br /><font color=\"darkorange\">LIMIT</font> <font color=\"darkcyan\">$top</font>",
          "intervalFactor": 2,
          "query": "SELECT\n    rand() as t,\n    substring(query,  1,  70) AS query,\n    avg(query_duration_ms) duration,\n    count() count\nFROM $table\nWHERE\n    $timeFilter\n    AND type in ($type)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\nGROUP BY query\nORDER BY duration desc\nLIMIT $top",
          "rawQuery": "SELECT     rand() as t,     substring(query,  1,  70) AS query,     avg(query_duration_ms) duration,     count() count FROM system.query_log WHERE     event_date >= toDate(1498209947) AND event_time >= toDateTime(1498209947)     AND type in (1,2,3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY query ORDER BY duration desc LIMIT 5",
          "refId": "A",
          "resultFormat": "time_series",
          "table": "query_log",
          "tableLoading": false
        }
      ],
      "title": "Top slow queries by type: $type; user: $user; query type: $query_type",
      "type": "table"
    },
    {
      "datasource": {
        "type": "vertamedia-clickhouse-datasource",
        "uid": "cef0o5s7s6z9ce"
      },
      "fieldConfig": {
        "defaults": {
          "custom": {
            "cellOptions": {
              "type": "auto"
            },
            "inspect": false
          },
          "decimals": 2,
          "displayName": "",
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": [
          {
            "matcher": {
              "id": "byName",
              "options": "Time"
            },
            "properties": [
              {
                "id": "displayName",
                "value": "Time"
              },
              {
                "id": "custom.hidden",
                "value": true
              },
              {
                "id": "custom.align"
              }
            ]
          },
          {
            "matcher": {
              "id": "byName",
              "options": "usage"
            },
            "properties": [
              {
                "id": "displayName",
                "value": "usage"
              },
              {
                "id": "unit",
                "value": "bytes"
              },
              {
                "id": "decimals",
                "value": 2
              },
              {
                "id": "custom.align"
              }
            ]
          },
          {
            "matcher": {
              "id": "byName",
              "options": "count"
            },
            "properties": [
              {
                "id": "unit",
                "value": "short"
              },
              {
                "id": "custom.align"
              }
            ]
          }
        ]
      },
      "gridPos": {
        "h": 11,
        "w": 8,
        "x": 8,
        "y": 8
      },
      "id": 19,
      "options": {
        "cellHeight": "sm",
        "footer": {
          "countRows": false,
          "fields": "",
          "reducer": [
            "sum"
          ],
          "show": false
        },
        "showHeader": true
      },
      "pluginVersion": "11.3.0",
      "targets": [
        {
          "database": "system",
          "datasource": {
            "type": "vertamedia-clickhouse-datasource",
            "uid": "cef0o5s7s6z9ce"
          },
          "dateColDataType": "event_date",
          "dateLoading": false,
          "dateTimeColDataType": "event_time",
          "datetimeLoading": false,
          "expr": "",
          "format": "table",
          "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">rand</font>() <font color=\"darkorange\">as</font> t,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">substring</font>(query,Ã‚ Ã‚ <font color=\"cornflowerblue\">1</font>,Ã‚ Ã‚ <font color=\"cornflowerblue\">70</font>) <font color=\"darkorange\">AS</font> query,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">avg</font>(memory_usage) usage,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">count</font>() <font color=\"navajowhite\">count</font><br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkcyan\">$timeFilter</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Ã‚ Ã‚ <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br /><font color=\"darkorange\">GROUP BY</font> query<br /><font color=\"darkorange\">ORDER BY</font> usage <font color=\"darkorange\">desc</font><br /><font color=\"darkorange\">LIMIT</font> <font color=\"darkcyan\">$top</font>",
          "intervalFactor": 2,
          "query": "SELECT\n    rand() as t,\n    substring(query,  1,  70) AS query,\n    avg(memory_usage) usage,\n    count() count\nFROM $table\nWHERE\n    $timeFilter\n    AND type in ($type)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\nGROUP BY query\nORDER BY usage desc\nLIMIT $top",
          "rawQuery": "SELECT     rand() as t,     substring(query,  1,  70) AS query,     avg(memory_usage) usage,     count() count FROM system.query_log WHERE     event_date >= toDate(1498209947) AND event_time >= toDateTime(1498209947)     AND type in (1,2,3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY query ORDER BY usage desc LIMIT 5",
          "refId": "A",
          "resultFormat": "time_series",
          "table": "query_log",
          "tableLoading": false
        }
      ],
      "title": "Top memory consumers by type: $type; user: $user; query type: $query_type",
      "type": "table"
    },
    {
      "datasource": {
        "type": "vertamedia-clickhouse-datasource",
        "uid": "cef0o5s7s6z9ce"
      },
      "fieldConfig": {
        "defaults": {
          "custom": {
            "cellOptions": {
              "type": "auto"
            },
            "inspect": false
          },
          "decimals": 2,
          "displayName": "",
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": [
          {
            "matcher": {
              "id": "byName",
              "options": "Time"
            },
            "properties": [
              {
                "id": "displayName",
                "value": "Time"
              },
              {
                "id": "custom.hidden",
                "value": true
              },
              {
                "id": "custom.align"
              }
            ]
          },
          {
            "matcher": {
              "id": "byName",
              "options": "type"
            },
            "properties": [
              {
                "id": "displayName",
                "value": "type"
              },
              {
                "id": "unit",
                "value": "none"
              },
              {
                "id": "custom.align"
              }
            ]
          },
          {
            "matcher": {
              "id": "byName",
              "options": "count"
            },
            "properties": [
              {
                "id": "unit",
                "value": "short"
              },
              {
                "id": "custom.align"
              }
            ]
          }
        ]
      },
      "gridPos": {
        "h": 11,
        "w": 8,
        "x": 16,
        "y": 8
      },
      "id": 20,
      "options": {
        "cellHeight": "sm",
        "footer": {
          "countRows": false,
          "fields": "",
          "reducer": [
            "sum"
          ],
          "show": false
        },
        "showHeader": true
      },
      "pluginVersion": "11.3.0",
      "targets": [
        {
          "database": "system",
          "datasource": {
            "type": "vertamedia-clickhouse-datasource",
            "uid": "cef0o5s7s6z9ce"
          },
          "dateColDataType": "event_date",
          "dateLoading": false,
          "dateTimeColDataType": "event_time",
          "datetimeLoading": false,
          "expr": "",
          "format": "table",
          "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">rand</font>() <font color=\"darkorange\">as</font> t,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">substring</font>(query,Ã‚ Ã‚ <font color=\"cornflowerblue\">1</font>,Ã‚ Ã‚ <font color=\"cornflowerblue\">70</font>) <font color=\"darkorange\">AS</font> query,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkorange\">type</font>,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">count</font>() <font color=\"navajowhite\">count</font><br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkcyan\">$timeFilter</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"cornflowerblue\">3</font>,<font color=\"cornflowerblue\">4</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Ã‚ Ã‚ <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br /><font color=\"darkorange\">GROUP BY</font><br />Ã‚ Ã‚ Ã‚ Ã‚ query,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkorange\">type</font><br /><font color=\"darkorange\">ORDER BY</font> <font color=\"navajowhite\">count</font> <font color=\"darkorange\">desc</font><br /><font color=\"darkorange\">LIMIT</font> <font color=\"darkcyan\">$top</font>",
          "intervalFactor": 2,
          "query": "SELECT\n    rand() as t,\n    substring(query,  1,  70) AS query,\n    type,\n    count() count\nFROM $table\nWHERE\n    $timeFilter\n    AND type in (3,4)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\nGROUP BY\n    query,\n    type\nORDER BY count desc\nLIMIT $top",
          "rawQuery": "SELECT     rand() as t,     substring(query,  1,  70) AS query,     type,     count() count FROM system.query_log WHERE     event_date >= toDate(1498209947) AND event_time >= toDateTime(1498209947)     AND type in (3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY     query,     type ORDER BY count desc LIMIT 5",
          "refId": "A",
          "resultFormat": "time_series",
          "table": "query_log",
          "tableLoading": false
        }
      ],
      "title": "Top failed queries by user: $user; query type: $query_type",
      "type": "table"
    },
    {
      "collapsed": false,
      "gridPos": {
        "h": 1,
        "w": 24,
        "x": 0,
        "y": 19
      },
      "id": 23,
      "panels": [],
      "title": "Request charts",
      "type": "row"
    },
    {
      "datasource": {
        "type": "vertamedia-clickhouse-datasource",
        "uid": "cef0o5s7s6z9ce"
      },
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisBorderShow": false,
            "axisCenteredZero": false,
            "axisColorMode": "text",
            "axisLabel": "",
            "axisPlacement": "auto",
            "barAlignment": 0,
            "barWidthFactor": 0.6,
            "drawStyle": "line",
            "fillOpacity": 10,
            "gradientMode": "none",
            "hideFrom": {
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "insertNulls": false,
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {
              "group": "A",
              "mode": "none"
            },
            "thresholdsStyle": {
              "mode": "off"
            }
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 7,
        "w": 12,
        "x": 0,
        "y": 20
      },
      "id": 14,
      "options": {
        "alertThreshold": true,
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom",
          "showLegend": true
        },
        "tooltip": {
          "mode": "multi",
          "sort": "none"
        }
      },
      "pluginVersion": "11.3.0",
      "targets": [
        {
          "database": "system",
          "datasource": {
            "type": "vertamedia-clickhouse-datasource",
            "uid": "cef0o5s7s6z9ce"
          },
          "dateColDataType": "event_date",
          "dateLoading": false,
          "dateTimeColDataType": "event_time",
          "datetimeLoading": false,
          "expr": "",
          "formattedQuery": "<font color=\"darkcyan\">$rate</font>(<font color=\"navajowhite\">count</font>() c)<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">where</font>Ã‚ Ã‚ <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Ã‚ Ã‚ <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))",
          "intervalFactor": 2,
          "query": "$rate(count() c)\nFROM $table\nwhere  type in ($type)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))",
          "rawQuery": "SELECT t, c/runningDifference(t/1000) cRate FROM ( SELECT (intDiv(toUInt32(event_time), 2) * 2) * 1000 as t, count() c FROM system.query_log WHERE event_date >= toDate(1498209895) AND event_time >= toDateTime(1498209895) AND   type in (1,2,3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY t ORDER BY t)",
          "refId": "A",
          "resultFormat": "time_series",
          "table": "query_log",
          "tableLoading": false
        }
      ],
      "title": "Reqs/s by type: $type; user: $user; query type: $query_type",
      "type": "timeseries"
    },
    {
      "datasource": {
        "type": "vertamedia-clickhouse-datasource",
        "uid": "cef0o5s7s6z9ce"
      },
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisBorderShow": false,
            "axisCenteredZero": false,
            "axisColorMode": "text",
            "axisLabel": "",
            "axisPlacement": "auto",
            "barAlignment": 0,
            "barWidthFactor": 0.6,
            "drawStyle": "line",
            "fillOpacity": 10,
            "gradientMode": "none",
            "hideFrom": {
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "insertNulls": false,
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": false,
            "stacking": {
              "group": "A",
              "mode": "none"
            },
            "thresholdsStyle": {
              "mode": "off"
            }
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "ms"
        },
        "overrides": [
          {
            "matcher": {
              "id": "byName",
              "options": "insert_duration"
            },
            "properties": [
              {
                "id": "custom.axisPlacement",
                "value": "right"
              }
            ]
          }
        ]
      },
      "gridPos": {
        "h": 7,
        "w": 12,
        "x": 12,
        "y": 20
      },
      "id": 16,
      "options": {
        "alertThreshold": true,
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom",
          "showLegend": true
        },
        "tooltip": {
          "mode": "multi",
          "sort": "none"
        }
      },
      "pluginVersion": "11.3.0",
      "targets": [
        {
          "database": "system",
          "datasource": {
            "type": "vertamedia-clickhouse-datasource",
            "uid": "cef0o5s7s6z9ce"
          },
          "dateColDataType": "event_date",
          "dateLoading": false,
          "dateTimeColDataType": "event_time",
          "datetimeLoading": false,
          "expr": "",
          "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkcyan\">$timeSeries</font> <font color=\"darkorange\">as</font> t,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">avg</font>(query_duration_ms)Ã‚ Ã‚ select_duration<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkcyan\">$timeFilter</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">2</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font> positionCaseInsensitive(query,Ã‚ Ã‚ <font color=\"lightgreen\">'select'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br /><font color=\"darkorange\">GROUP BY</font> t<br /><font color=\"darkorange\">ORDER BY</font> t",
          "intervalFactor": 2,
          "query": "SELECT\n    $timeSeries as t,\n    avg(query_duration_ms)  select_duration\nFROM $table\nWHERE\n    $timeFilter\n    AND type = 2\n    and positionCaseInsensitive(query,  'select') = 1\n    and initial_user in ($user)\nGROUP BY t\nORDER BY t",
          "rawQuery": "SELECT     (intDiv(toUInt32(event_time), 2) * 2) * 1000 as t,     avg(query_duration_ms)  select_duration FROM system.query_log WHERE     event_date >= toDate(1498209895) AND event_time >= toDateTime(1498209895)     AND type = 2     and positionCaseInsensitive(query,  'select') = 1     and initial_user in ('default') GROUP BY t ORDER BY t",
          "refId": "A",
          "resultFormat": "time_series",
          "table": "query_log",
          "tableLoading": false
        },
        {
          "database": "system",
          "datasource": {
            "type": "vertamedia-clickhouse-datasource",
            "uid": "cef0o5s7s6z9ce"
          },
          "dateColDataType": "event_date",
          "dateLoading": false,
          "dateTimeColDataType": "event_time",
          "datetimeLoading": false,
          "expr": "",
          "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkcyan\">$timeSeries</font> <font color=\"darkorange\">as</font> t,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">avg</font>(query_duration_ms) insert_duration<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"darkcyan\">$timeFilter</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">2</font><br /><font color=\"yellow\">and</font> positionCaseInsensitive(query,Ã‚ Ã‚ <font color=\"lightgreen\">'insert into'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br /><font color=\"darkorange\">GROUP BY</font> t<br /><font color=\"darkorange\">ORDER BY</font> t",
          "intervalFactor": 2,
          "query": "SELECT\n    $timeSeries as t,\n    avg(query_duration_ms) insert_duration\nFROM $table\nWHERE\n    $timeFilter\n    AND type = 2\nand positionCaseInsensitive(query,  'insert into') = 1\n    and initial_user in ($user)\nGROUP BY t\nORDER BY t",
          "rawQuery": "SELECT     (intDiv(toUInt32(event_time), 2) * 2) * 1000 as t,     avg(query_duration_ms) insert_duration FROM system.query_log WHERE     event_date >= toDate(1498209895) AND event_time >= toDateTime(1498209895)     AND type = 2 and positionCaseInsensitive(query,  'insert into') = 1     and initial_user in ('default') GROUP BY t ORDER BY t",
          "refId": "B",
          "resultFormat": "time_series",
          "table": "query_log",
          "tableLoading": false
        }
      ],
      "title": "Query duration by type: $type; user: $user",
      "type": "timeseries"
    },
    {
      "collapsed": false,
      "gridPos": {
        "h": 1,
        "w": 24,
        "x": 0,
        "y": 27
      },
      "id": 24,
      "panels": [],
      "title": "Query log table",
      "type": "row"
    },
    {
      "datasource": {
        "type": "vertamedia-clickhouse-datasource",
        "uid": "cef0o5s7s6z9ce"
      },
      "fieldConfig": {
        "defaults": {
          "custom": {
            "cellOptions": {
              "type": "auto"
            },
            "inspect": false
          },
          "decimals": 2,
          "displayName": "",
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": [
          {
            "matcher": {
              "id": "byName",
              "options": "Time"
            },
            "properties": [
              {
                "id": "displayName",
                "value": "Time"
              },
              {
                "id": "unit",
                "value": "time: YYYY-MM-DD HH:mm:ss"
              },
              {
                "id": "custom.align"
              }
            ]
          },
          {
            "matcher": {
              "id": "byName",
              "options": "duration"
            },
            "properties": [
              {
                "id": "unit",
                "value": "ms"
              },
              {
                "id": "custom.align"
              }
            ]
          },
          {
            "matcher": {
              "id": "byName",
              "options": "memory"
            },
            "properties": [
              {
                "id": "unit",
                "value": "bytes"
              },
              {
                "id": "decimals",
                "value": 2
              },
              {
                "id": "custom.align"
              }
            ]
          }
        ]
      },
      "gridPos": {
        "h": 7,
        "w": 24,
        "x": 0,
        "y": 28
      },
      "id": 21,
      "options": {
        "cellHeight": "sm",
        "footer": {
          "countRows": false,
          "fields": "",
          "reducer": [
            "sum"
          ],
          "show": false
        },
        "showHeader": true
      },
      "pluginVersion": "11.3.0",
      "targets": [
        {
          "database": "system",
          "datasource": {
            "type": "vertamedia-clickhouse-datasource",
            "uid": "cef0o5s7s6z9ce"
          },
          "dateColDataType": "event_date",
          "dateLoading": false,
          "dateTimeColDataType": "event_time",
          "datetimeLoading": false,
          "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Ã‚ Ã‚ Ã‚ Ã‚ event_time,<br />Ã‚ Ã‚ Ã‚ Ã‚ user,<br />Ã‚ Ã‚ Ã‚ Ã‚ query_duration_ms duration,<br />Ã‚ Ã‚ Ã‚ Ã‚ memory_usage memory,<br />Ã‚ Ã‚ Ã‚ Ã‚ if(exception<font color=\"yellow\">!=</font><font color=\"lightgreen\">''</font>, <font color=\"lightgreen\">'fail'</font>, <font color=\"lightgreen\">'success'</font>) result,<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"navajowhite\">concat</font>(<font color=\"navajowhite\">substring</font>(query,<font color=\"cornflowerblue\">1</font>,<font color=\"cornflowerblue\">120</font>), <font color=\"lightgreen\">'...'</font>) query<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font> <font color=\"darkcyan\">$timeFilter</font><br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Ã‚ Ã‚ Ã‚ Ã‚ <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Ã‚ Ã‚ <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br /><font color=\"darkorange\">ORDER BY</font> event_time <font color=\"darkorange\">DESC</font><br /><font color=\"darkorange\">LIMIT</font> <font color=\"cornflowerblue\">1000</font>",
          "intervalFactor": 1,
          "query": "SELECT\n    event_time,\n    user,\n    query_duration_ms duration,\n    memory_usage memory,\n    if(exception!='', 'fail', 'success') result,\n    concat(substring(query,1,120), '...') query\nFROM $table\nWHERE $timeFilter\n    AND type in ($type)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\nORDER BY event_time DESC\nLIMIT 1000",
          "rawQuery": "SELECT     event_time,     user,     query_duration_ms duration,     memory_usage memory,     if(exception!='', 'fail', 'success') result,     concat(substring(query,1,120), '...') query FROM system.query_log WHERE event_date >= toDate(1498209895) AND event_time >= toDateTime(1498209895)     AND type in (1,2,3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) ORDER BY event_time DESC LIMIT 1000",
          "refId": "A",
          "resultFormat": "time_series",
          "table": "query_log",
          "tableLoading": false
        }
      ],
      "title": "Query log by type: $type; user: $user; query type: $query_type",
      "type": "table"
    }
  ],
  "preload": false,
  "refresh": "5s",
  "schemaVersion": 40,
  "tags": [
    "clickhouse",
    "performance"
  ],
  "templating": {
    "list": [
      {
        "auto": true,
        "auto_count": 100,
        "auto_min": "1m",
        "current": {
          "text": "5m",
          "value": "5m"
        },
        "hide": 2,
        "name": "interval",
        "options": [
          {
            "selected": false,
            "text": "auto",
            "value": "$__auto_interval_interval"
          },
          {
            "selected": true,
            "text": "5m",
            "value": "5m"
          }
        ],
        "query": "5m",
        "refresh": 2,
        "type": "interval"
      },
      {
        "current": {
          "tags": [],
          "text": [
            "All"
          ],
          "value": [
            "$__all"
          ]
        },
        "includeAll": true,
        "label": "type",
        "multi": true,
        "name": "type",
        "options": [
          {
            "selected": true,
            "text": "All",
            "value": "$__all"
          },
          {
            "selected": false,
            "text": "1",
            "value": "1"
          },
          {
            "selected": false,
            "text": "2",
            "value": "2"
          },
          {
            "selected": false,
            "text": "3",
            "value": "3"
          },
          {
            "selected": false,
            "text": "4",
            "value": "4"
          }
        ],
        "query": "1,2,3,4",
        "type": "custom"
      },
      {
        "current": {
          "tags": [],
          "text": "5",
          "value": "5"
        },
        "includeAll": false,
        "label": "top elements",
        "name": "top",
        "options": [
          {
            "selected": true,
            "text": "5",
            "value": "5"
          },
          {
            "selected": false,
            "text": "10",
            "value": "10"
          },
          {
            "selected": false,
            "text": "15",
            "value": "15"
          },
          {
            "selected": false,
            "text": "20",
            "value": "20"
          },
          {
            "selected": false,
            "text": "25",
            "value": "25"
          },
          {
            "selected": false,
            "text": "30",
            "value": "30"
          }
        ],
        "query": "5,10,15,20,25,30",
        "type": "custom"
      },
      {
        "current": {
          "text": "All",
          "value": "$__all"
        },
        "datasource": "Altinity plugin for ClickHouse UAT",
        "definition": "",
        "includeAll": true,
        "label": "initial user",
        "multi": true,
        "name": "user",
        "options": [],
        "query": "select distinct initial_user from system.query_log where event_date >= today()-3 and event_time > toDateTime(today()-3)",
        "refresh": 1,
        "regex": "",
        "type": "query"
      },
      {
        "current": {
          "text": "all",
          "value": "all"
        },
        "includeAll": false,
        "label": "query type",
        "name": "query_type",
        "options": [
          {
            "selected": true,
            "text": "all",
            "value": "all"
          },
          {
            "selected": false,
            "text": "select",
            "value": "select"
          },
          {
            "selected": false,
            "text": "insert",
            "value": "insert"
          }
        ],
        "query": "all,select,insert",
        "type": "custom"
      }
    ]
  },
  "time": {
    "from": "now-1h",
    "to": "now"
  },
  "timepicker": {},
  "timezone": "browser",
  "title": "ClickHouse Queries UAT",
  "uid": "w4Hs2T9qB",
  "version": 1,
  "weekStart": ""
}
